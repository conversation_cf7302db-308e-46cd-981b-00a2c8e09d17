<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxHeight="600dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 標題 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="選擇字體"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <!-- 字體預覽 -->
        <TextView
            android:id="@+id/text_font_preview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@android:color/white"
            android:padding="16dp"
            android:text="字體預覽：這是一段示例文字，用於預覽字體效果。The quick brown fox jumps over the lazy dog."
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:lineSpacingExtra="4dp"
            android:maxHeight="120dp"
            android:scrollbars="vertical" />

        <!-- 字體列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_fonts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:maxHeight="200dp"
            android:scrollbars="vertical" />

        <!-- 按鈕區域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="取消"
                android:textColor="@color/text_secondary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_apply"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="套用"
                android:backgroundTint="@color/primary_blue" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
