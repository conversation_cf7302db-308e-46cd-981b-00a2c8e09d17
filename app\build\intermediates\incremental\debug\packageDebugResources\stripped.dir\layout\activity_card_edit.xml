<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/button_cancel"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="取消"
                    android:src="@drawable/ic_close"
                    android:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/button_save"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="保存"
                    android:src="@drawable/ic_check"
                    android:tint="@android:color/white" />

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 題目區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="題目"
                        android:textColor="@color/primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <ImageButton
                        android:id="@+id/button_rotate_question_image"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="逆時針旋轉圖片90度"
                        android:src="@drawable/ic_rotate_90"
                        android:tint="@color/primary" />

                    <ImageButton
                        android:id="@+id/button_ocr_question"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="OCR文字識別"
                        android:src="@drawable/ic_ocr"
                        android:tint="@color/primary" />

                    <ImageButton
                        android:id="@+id/button_add_question_image"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="添加題目圖片"
                        android:src="@drawable/ic_image"
                        android:tint="@color/primary" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.erroranalysis.app.ui.widgets.RichTextEditText
                        android:id="@+id/edit_question"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="輸入題目內容，可插入圖片"
                        android:inputType="textMultiLine|textCapSentences"
                        android:maxLines="10"
                        android:minLines="4"
                        android:scrollbars="vertical"
                        android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
                        android:scrollbarTrackVertical="@drawable/scrollbar_track"
                        android:scrollbarSize="28dp"
                        android:scrollbarStyle="insideOverlay"
                        android:fadeScrollbars="false"
                        android:scrollbarAlwaysDrawVerticalTrack="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 答案區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="答案"
                        android:textColor="@color/primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <ImageButton
                        android:id="@+id/button_ocr_answer"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="OCR文字識別"
                        android:src="@drawable/ic_ocr"
                        android:tint="@color/primary" />

                    <ImageButton
                        android:id="@+id/button_add_answer_image"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="添加答案圖片"
                        android:src="@drawable/ic_image"
                        android:tint="@color/primary" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.erroranalysis.app.ui.widgets.RichTextEditText
                        android:id="@+id/edit_answer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="輸入答案內容，可插入圖片"
                        android:inputType="textMultiLine|textCapSentences"
                        android:maxLines="10"
                        android:minLines="4"
                        android:scrollbars="vertical"
                        android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
                        android:scrollbarTrackVertical="@drawable/scrollbar_track"
                        android:scrollbarSize="28dp"
                        android:scrollbarStyle="insideOverlay"
                        android:fadeScrollbars="false"
                        android:scrollbarAlwaysDrawVerticalTrack="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- AI解答區域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <!-- AI解答按鈕 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_ai_answer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="🤖 AI解答"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:backgroundTint="@color/primary"
                        android:textColor="@android:color/white"
                        app:cornerRadius="20dp"
                        app:icon="@null"
                        style="@style/Widget.MaterialComponents.Button" />

                    <!-- AI解答輸入框 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/layout_ai_answer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint=""
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.erroranalysis.app.ui.widgets.RichTextEditText
                        android:id="@+id/edit_ai_answer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="AI正在思考中..."
                        android:inputType="textMultiLine"
                        android:maxLines="15"
                        android:minLines="4"
                        android:scrollbars="vertical"
                        android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
                        android:scrollbarTrackVertical="@drawable/scrollbar_track"
                        android:scrollbarSize="28dp"
                        android:scrollbarStyle="insideOverlay"
                        android:fadeScrollbars="false"
                        android:scrollbarAlwaysDrawVerticalTrack="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp"
                        android:enabled="false"
                        android:focusable="false" />

                </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 標籤區域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="標籤"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_tags"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="用逗號分隔多個標籤，例如：代數, 方程式"
                        android:inputType="text"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 底部空白區域，確保內容不被鍵盤遮擋 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="100dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
