<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 相機權限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-feature android:name="android.hardware.camera" android:required="true" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    
    <!-- 存儲權限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    
    <!-- 網絡權限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".ErrorAnalysisApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_app_icon"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_app_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.ErrorAnalysisApp">
        
        <!-- 主界面 -->
        <activity
            android:name=".ui.main.SimpleMainActivity"
            android:exported="true"
            android:theme="@style/Theme.ErrorAnalysisApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- 相機界面 -->
        <activity
            android:name=".ui.camera.CameraActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 簡化相機測試界面 -->
        <activity
            android:name=".ui.camera.SimpleCameraActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 照片編輯界面 -->
        <activity
            android:name=".ui.camera.PhotoEditActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 裁剪選取框測試界面 -->
        <activity
            android:name=".ui.camera.CropOverlayTestActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
            
        <!-- 題目選擇界面 -->
        <activity
            android:name=".ui.selection.QuestionSelectionActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
            
        <!-- 分析結果界面 -->
        <activity
            android:name=".ui.analysis.AnalysisActivity"
            android:exported="false" />
            


        <!-- 錯題庫主界面（簡化版） -->
        <activity
            android:name=".ui.study.SimpleStudyActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|screenLayout"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 卡組詳細界面 -->
        <activity
            android:name=".ui.study.DeckDetailActivity"
            android:exported="false"
            android:parentActivityName=".ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 卡片編輯界面 -->
        <activity
            android:name=".ui.study.CardEditActivity"
            android:exported="false"
            android:parentActivityName=".ui.study.DeckDetailActivity"
            android:theme="@style/Theme.ErrorAnalysisApp"
            android:windowSoftInputMode="adjustResize" />

        <!-- 卡片檢視界面 -->
        <activity
            android:name=".ui.study.CardViewerActivity"
            android:exported="false"
            android:parentActivityName=".ui.study.DeckDetailActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 批次匯入界面 -->
        <activity
            android:name=".ui.study.BatchImportActivity"
            android:exported="false"
            android:parentActivityName=".ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 設置頁面 -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />



    </application>

</manifest>
