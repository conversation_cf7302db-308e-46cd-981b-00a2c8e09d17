1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.erroranalysis.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 相機權限 -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:5:5-65
12-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:5:22-62
13
14    <uses-feature
14-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:5-84
15        android:name="android.hardware.camera"
15-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:19-57
16        android:required="true" />
16-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:58-81
17    <uses-feature
17-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:5-95
18        android:name="android.hardware.camera.autofocus"
18-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:19-67
19        android:required="false" />
19-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:68-92
20
21    <!-- 存儲權限 -->
22    <uses-permission
22-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:10:5-11:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:10:22-78
24        android:maxSdkVersion="28" />
24-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:11:9-35
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:12:5-80
25-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:12:22-77
26
27    <!-- 網絡權限 -->
28    <uses-permission android:name="android.permission.INTERNET" />
28-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:15:5-67
28-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:15:22-64
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:16:5-79
29-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:16:22-76
30
31    <permission
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
32        android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
36
37    <application
37-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:18:5-120:19
38        android:name="com.erroranalysis.app.ErrorAnalysisApplication"
38-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:19:9-49
39        android:allowBackup="true"
39-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:20:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:icon="@drawable/ic_app_icon"
43-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:21:9-45
44        android:label="@string/app_name"
44-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:22:9-41
45        android:roundIcon="@drawable/ic_app_icon"
45-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:23:9-50
46        android:supportsRtl="true"
46-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:24:9-35
47        android:theme="@style/Theme.ErrorAnalysisApp" >
47-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:25:9-54
48
49        <!-- 主界面 -->
50        <activity
50-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:28:9-36:20
51            android:name="com.erroranalysis.app.ui.main.SimpleMainActivity"
51-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:29:13-55
52            android:exported="true"
52-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:30:13-36
53            android:theme="@style/Theme.ErrorAnalysisApp" >
53-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:31:13-58
54            <intent-filter>
54-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:32:13-35:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:33:17-69
55-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:33:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:34:17-77
57-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:34:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- 相機界面 -->
62        <activity
62-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:39:9-42:52
63            android:name="com.erroranalysis.app.ui.camera.CameraActivity"
63-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:40:13-53
64            android:exported="false"
64-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:41:13-37
65            android:screenOrientation="portrait" />
65-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:42:13-49
66
67        <!-- 簡化相機測試界面 -->
68        <activity
68-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:45:9-48:52
69            android:name="com.erroranalysis.app.ui.camera.SimpleCameraActivity"
69-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:46:13-59
70            android:exported="false"
70-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:47:13-37
71            android:screenOrientation="portrait" />
71-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:48:13-49
72
73        <!-- 照片編輯界面 -->
74        <activity
74-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:51:9-54:52
75            android:name="com.erroranalysis.app.ui.camera.PhotoEditActivity"
75-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:52:13-56
76            android:exported="false"
76-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:53:13-37
77            android:screenOrientation="portrait" />
77-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:54:13-49
78
79        <!-- 裁剪選取框測試界面 -->
80        <activity
80-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:57:9-60:52
81            android:name="com.erroranalysis.app.ui.camera.CropOverlayTestActivity"
81-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:58:13-62
82            android:exported="false"
82-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:59:13-37
83            android:screenOrientation="portrait" />
83-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:60:13-49
84
85        <!-- 題目選擇界面 -->
86        <activity
86-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:63:9-66:52
87            android:name="com.erroranalysis.app.ui.selection.QuestionSelectionActivity"
87-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:64:13-67
88            android:exported="false"
88-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:65:13-37
89            android:screenOrientation="portrait" />
89-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:66:13-49
90
91        <!-- 分析結果界面 -->
92        <activity
92-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:69:9-71:40
93            android:name="com.erroranalysis.app.ui.analysis.AnalysisActivity"
93-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:70:13-57
94            android:exported="false" />
94-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:71:13-37
95
96        <!-- 錯題庫主界面（簡化版） -->
97        <activity
97-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:76:9-80:61
98            android:name="com.erroranalysis.app.ui.study.SimpleStudyActivity"
98-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:77:13-57
99            android:configChanges="orientation|screenSize|screenLayout"
99-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:79:13-72
100            android:exported="false"
100-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:78:13-37
101            android:theme="@style/Theme.ErrorAnalysisApp" />
101-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:80:13-58
102
103        <!-- 卡組詳細界面 -->
104        <activity
104-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:83:9-87:61
105            android:name="com.erroranalysis.app.ui.study.DeckDetailActivity"
105-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:84:13-56
106            android:exported="false"
106-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:85:13-37
107            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
107-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:86:13-71
108            android:theme="@style/Theme.ErrorAnalysisApp" />
108-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:87:13-58
109
110        <!-- 卡片編輯界面 -->
111        <activity
111-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:90:9-95:58
112            android:name="com.erroranalysis.app.ui.study.CardEditActivity"
112-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:91:13-54
113            android:exported="false"
113-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:92:13-37
114            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
114-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:93:13-70
115            android:theme="@style/Theme.ErrorAnalysisApp"
115-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:94:13-58
116            android:windowSoftInputMode="adjustResize" />
116-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:95:13-55
117
118        <!-- 卡片檢視界面 -->
119        <activity
119-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:98:9-102:61
120            android:name="com.erroranalysis.app.ui.study.CardViewerActivity"
120-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:99:13-56
121            android:exported="false"
121-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:100:13-37
122            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
122-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:101:13-70
123            android:theme="@style/Theme.ErrorAnalysisApp" />
123-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:102:13-58
124
125        <!-- 批次匯入界面 -->
126        <activity
126-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:105:9-109:61
127            android:name="com.erroranalysis.app.ui.study.BatchImportActivity"
127-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:106:13-57
128            android:exported="false"
128-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:107:13-37
129            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
129-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:108:13-71
130            android:theme="@style/Theme.ErrorAnalysisApp" />
130-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:109:13-58
131
132        <!-- 設置頁面 -->
133        <activity
133-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:112:9-116:61
134            android:name="com.erroranalysis.app.ui.settings.SettingsActivity"
134-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:113:13-57
135            android:exported="false"
135-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:114:13-37
136            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
136-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:115:13-71
137            android:theme="@style/Theme.ErrorAnalysisApp" />
137-->C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:116:13-58
138
139        <!--
140        Service for holding metadata. Cannot be instantiated.
141        Metadata will be merged from other manifests.
142        -->
143        <service
143-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
144            android:name="androidx.camera.core.impl.MetadataHolderService"
144-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:30:13-75
145            android:enabled="false"
145-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:31:13-36
146            android:exported="false" >
146-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:32:13-37
147            <meta-data
147-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
148                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
148-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
149                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
149-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
150        </service>
151        <service
151-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
152            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
152-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
153            android:directBootAware="true"
153-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:17:13-43
154            android:exported="false" >
154-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
155            <meta-data
155-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
156                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
156-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
158            <meta-data
158-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
159                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
159-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
161            <meta-data
161-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:20:13-22:85
162                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
162-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:21:17-120
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:22:17-82
164        </service>
165
166        <provider
166-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:9:9-13:38
167            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
167-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:10:13-78
168            android:authorities="com.erroranalysis.app.mlkitinitprovider"
168-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:11:13-69
169            android:exported="false"
169-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:12:13-37
170            android:initOrder="99" />
170-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:13:13-35
171
172        <activity
172-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
180
181        <provider
181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
182            android:name="androidx.startup.InitializationProvider"
182-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
183            android:authorities="com.erroranalysis.app.androidx-startup"
183-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
184            android:exported="false" >
184-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
185            <meta-data
185-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
186                android:name="androidx.emoji2.text.EmojiCompatInitializer"
186-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
187                android:value="androidx.startup" />
187-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
188            <meta-data
188-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
189                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
189-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
190                android:value="androidx.startup" />
190-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
191            <meta-data
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
193                android:value="androidx.startup" />
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
194        </provider>
195
196        <receiver
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
197            android:name="androidx.profileinstaller.ProfileInstallReceiver"
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
198            android:directBootAware="false"
198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
199            android:enabled="true"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
200            android:exported="true"
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
201            android:permission="android.permission.DUMP" >
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
203                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
206                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
209                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
212                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
213            </intent-filter>
214        </receiver>
215
216        <service
216-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
217            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
217-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
218            android:exported="false" >
218-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
219            <meta-data
219-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
220                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
220-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
221                android:value="cct" />
221-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
222        </service>
223        <service
223-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
224            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
224-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
225            android:exported="false"
225-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
226            android:permission="android.permission.BIND_JOB_SERVICE" >
226-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
227        </service>
228
229        <receiver
229-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
230            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
230-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
231            android:exported="false" />
231-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
232    </application>
233
234</manifest>
