package com.erroranalysis.app.ui.study.adapters

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemStudyCardBinding
import com.erroranalysis.app.ui.study.StudyCard
import java.text.SimpleDateFormat
import java.util.*

/**
 * 學習卡片適配器
 */
class StudyCardAdapter(
    private val onCardClick: (StudyCard) -> Unit,
    private val onCardLongClick: (StudyCard) -> Unit,
    private val onStarClick: (StudyCard) -> Unit
) : ListAdapter<StudyCard, StudyCardAdapter.CardViewHolder>(CardDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CardViewHolder {
        val binding = ItemStudyCardBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return CardViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: CardViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class CardViewHolder(
        private val binding: ItemStudyCardBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(card: StudyCard) {
            // 顯示純文字預覽，不顯示圖片
            binding.textQuestion.text = getPlainTextPreview(card.question)

            // 預覽模式：隱藏答案和標籤
            binding.textAnswer.visibility = android.view.View.GONE
            binding.textTags.visibility = android.view.View.GONE
            
            // 顯示熟練度
            binding.textDifficulty.text = card.mastery.displayName
            try {
                val color = Color.parseColor(card.mastery.color)
                binding.textDifficulty.setTextColor(color)
            } catch (e: Exception) {
                // 使用預設顏色
            }
            
            // 顯示掌握程度
            binding.progressMastery.progress = (card.masteryLevel * 20) // 0-5 轉換為 0-100
            binding.textMasteryLevel.text = when (card.masteryLevel) {
                0 -> "未學習"
                1 -> "初學"
                2 -> "理解"
                3 -> "熟悉"
                4 -> "掌握"
                5 -> "精通"
                else -> "未知"
            }
            
            // 顯示統計資訊
            if (card.reviewCount > 0) {
                val accuracy = (card.accuracyRate * 100).toInt()
                binding.textStats.text = "複習 ${card.reviewCount} 次 • 正確率 ${accuracy}%"
                binding.textStats.visibility = android.view.View.VISIBLE
            } else {
                binding.textStats.visibility = android.view.View.GONE
            }
            
            // 顯示建立時間
            val dateFormat = SimpleDateFormat("MM/dd", Locale.getDefault())
            binding.textCreatedTime.text = dateFormat.format(Date(card.createdTime))

            // 設置星星狀態
            if (card.isStarred) {
                binding.iconStar.setImageResource(com.erroranalysis.app.R.drawable.ic_star_filled)
            } else {
                binding.iconStar.setImageResource(com.erroranalysis.app.R.drawable.ic_star_outline)
            }

            // 設置星星容器點擊事件 - 更大的點擊區域
            binding.starContainer.setOnClickListener { view ->
                // 阻止事件傳播到父視圖
                view.isPressed = false
                onStarClick(card)

                // 添加視覺反饋到星星圖標
                binding.iconStar.animate()
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(100)
                    .withEndAction {
                        binding.iconStar.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start()
                    }
                    .start()

                // 添加輕微的觸覺反饋
                view.performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY)
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                onCardClick(card)
            }

            binding.root.setOnLongClickListener {
                onCardLongClick(card)
                true
            }
            
            // 設置卡片狀態指示器
            when {
                card.isMastered -> {
                    binding.indicatorStatus.setBackgroundColor(Color.parseColor("#4CAF50"))
                }
                card.needsReview -> {
                    binding.indicatorStatus.setBackgroundColor(Color.parseColor("#FF9800"))
                }
                else -> {
                    binding.indicatorStatus.setBackgroundColor(Color.parseColor("#2196F3"))
                }
            }
        }

        /**
         * 獲取純文字預覽，移除圖片和JSON格式
         */
        private fun getPlainTextPreview(content: String): String {
            return try {
                // 檢查是否為JSON格式
                if (content.trim().startsWith("[")) {
                    // 解析JSON並提取純文字
                    val contentItems = com.google.gson.Gson().fromJson(
                        content,
                        Array<ContentItem>::class.java
                    )

                    val textBuilder = StringBuilder()
                    contentItems?.forEach { item ->
                        when (item.type) {
                            "text" -> {
                                textBuilder.append(item.content)
                            }
                            "image" -> {
                                textBuilder.append("[圖片]")
                            }
                        }
                    }

                    val plainText = textBuilder.toString().trim()
                    // 限制預覽長度
                    if (plainText.length > 100) {
                        plainText.substring(0, 100) + "..."
                    } else {
                        plainText
                    }
                } else {
                    // 純文字內容，直接返回（限制長度）
                    if (content.length > 100) {
                        content.substring(0, 100) + "..."
                    } else {
                        content
                    }
                }
            } catch (e: Exception) {
                // 解析失敗，返回原始內容的前100字符
                if (content.length > 100) {
                    content.substring(0, 100) + "..."
                } else {
                    content
                }
            }
        }
    }

    /**
     * 內容項目資料類別（用於JSON解析）
     */
    private data class ContentItem(
        val type: String,
        val content: String
    )

    private class CardDiffCallback : DiffUtil.ItemCallback<StudyCard>() {
        override fun areItemsTheSame(oldItem: StudyCard, newItem: StudyCard): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: StudyCard, newItem: StudyCard): Boolean {
            return oldItem == newItem
        }
    }
}
