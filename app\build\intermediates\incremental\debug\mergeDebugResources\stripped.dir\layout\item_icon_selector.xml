<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="6dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_icon"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        android:clickable="true"
        android:focusable="true"
        app:cardCornerRadius="28dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/primary_blue"
        app:strokeWidth="0dp"
        app:cardBackgroundColor="@android:color/transparent">

        <TextView
            android:id="@+id/text_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textSize="26sp"
            android:text="🎯" />

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/text_icon_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="目標"
        android:textSize="10sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
