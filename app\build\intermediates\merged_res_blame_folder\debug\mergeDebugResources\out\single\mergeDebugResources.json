[{"merged": "com.erroranalysis.app-debug-45:/layout_activity_main.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_main.xml"}, {"merged": "com.erroranalysis.app-debug-45:/raw_chenyu_luoyan_regular.ttf.flat", "source": "com.erroranalysis.app-main-47:/raw/chenyu_luoyan_regular.ttf"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_clock.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_clock.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_create_deck_simple.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_create_deck_simple.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_circle_button_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/circle_button_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_chart.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_chart.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_gradient_primary.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/gradient_primary.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_settings.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_settings.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_lightbulb.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_lightbulb.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_search.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_search.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_app_icon.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_app_icon.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_camera_overlay_border.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/camera_overlay_border.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_check.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_check.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_filter_active.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_filter_active.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_launcher_foreground.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_card_edit.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_card_edit.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_theme.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_theme.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_card_viewer.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_card_viewer.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_add.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_add.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_color.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_color.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_rotate_90.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_rotate_90.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_palette.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_palette.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_copy.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_copy.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_bg_tag_rounded.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/bg_tag_rounded.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_camera.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_camera.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_focus_indicator_too_close.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/focus_indicator_too_close.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_favorite_filled.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_favorite_filled.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_ai_white.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_ai_white.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_circle_number_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/circle_number_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_font_selector.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_font_selector.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_deck_cover_gradient.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/deck_cover_gradient.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_scrollbar_thumb.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/scrollbar_thumb.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_export.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_export.xml"}, {"merged": "com.erroranalysis.app-debug-45:/menu_menu_main.xml.flat", "source": "com.erroranalysis.app-main-47:/menu/menu_main.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_icon_selector.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_icon_selector.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_batch_import.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_batch_import.xml"}, {"merged": "com.erroranalysis.app-debug-45:/font_noto_sans_tc.xml.flat", "source": "com.erroranalysis.app-main-47:/font/noto_sans_tc.xml"}, {"merged": "com.erroranalysis.app-debug-45:/font_chenyu_luoyan.xml.flat", "source": "com.erroranalysis.app-main-47:/font/chenyu_luoyan.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_circle_background_primary.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/circle_background_primary.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_crop_circle.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_crop_circle.xml"}, {"merged": "com.erroranalysis.app-debug-45:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.erroranalysis.app-main-47:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_capture_button_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/capture_button_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_ai.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_ai.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_font.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_font.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_arrow_back.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_arrow_back.xml"}, {"merged": "com.erroranalysis.app-debug-45:/menu_bottom_navigation.xml.flat", "source": "com.erroranalysis.app-main-47:/menu/bottom_navigation.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_close.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_close.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_camera.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_camera.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_card_filter.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_card_filter.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_create_deck.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_create_deck.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_edit.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_edit.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_simple_create_deck.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_simple_create_deck.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_filter.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_filter.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_star_outline.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_star_outline.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_volume_up.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_volume_up.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_btn_crop_circle.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/btn_crop_circle.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_person.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_person.xml"}, {"merged": "com.erroranalysis.app-debug-45:/xml_data_extraction_rules.xml.flat", "source": "com.erroranalysis.app-main-47:/xml/data_extraction_rules.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_scissors.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_scissors.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_bg_hint_chip.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/bg_hint_chip.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_focus_indicator_too_far.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/focus_indicator_too_far.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_scrollbar_track.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/scrollbar_track.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_photo_library.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_photo_library.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_font_test_layout.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/font_test_layout.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_btn_save_circle.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/btn_save_circle.xml"}, {"merged": "com.erroranalysis.app-debug-45:/xml_backup_rules.xml.flat", "source": "com.erroranalysis.app-main-47:/xml/backup_rules.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_create_card.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_create_card.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_delete.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_delete.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_template.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_template.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_expand_less.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_expand_less.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_expand_more.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_expand_more.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_photo_edit.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_photo_edit.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_deck_detail.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_deck_detail.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_launcher_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_launcher_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_check_circle.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_check_circle.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_empty_deck.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_empty_deck.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_home.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_home.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_filter_inactive.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_filter_inactive.xml"}, {"merged": "com.erroranalysis.app-debug-45:/font_noto_sans_tc_regular.ttf.flat", "source": "com.erroranalysis.app-main-47:/font/noto_sans_tc_regular.ttf"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_study_card.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_study_card.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_bg_card_content.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/bg_card_content.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_book.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_book.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_focus_indicator_capturing.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/focus_indicator_capturing.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_simple_study.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_simple_study.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_settings.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_settings.xml"}, {"merged": "com.erroranalysis.app-debug-45:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.erroranalysis.app-main-47:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_statistics.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_statistics.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_info.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_info.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_save_circle.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_save_circle.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_image.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_image.xml"}, {"merged": "com.erroranalysis.app-debug-45:/font_font_family_config.xml.flat", "source": "com.erroranalysis.app-main-47:/font/font_family_config.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_search_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/search_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_chip_background.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/chip_background.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_archive.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_archive.xml"}, {"merged": "com.erroranalysis.app-debug-45:/color_bottom_nav_color.xml.flat", "source": "com.erroranalysis.app-main-47:/color/bottom_nav_color.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_star_filled.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_star_filled.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_crop_overlay_test.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_crop_overlay_test.xml"}, {"merged": "com.erroranalysis.app-debug-45:/menu_menu_study_main.xml.flat", "source": "com.erroranalysis.app-main-47:/menu/menu_study_main.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_font_option.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_font_option.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_activity_font_test.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/activity_font_test.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_circle_button_inward.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/circle_button_inward.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_folder.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_folder.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_ocr.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_ocr.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_quick_template.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_quick_template.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_font_test.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_font_test.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_dialog_deck_filter.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/dialog_deck_filter.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_volume_off.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_volume_off.xml"}, {"merged": "com.erroranalysis.app-debug-45:/layout_item_simple_deck.xml.flat", "source": "com.erroranalysis.app-main-47:/layout/item_simple_deck.xml"}, {"merged": "com.erroranalysis.app-debug-45:/drawable_ic_sort.xml.flat", "source": "com.erroranalysis.app-main-47:/drawable/ic_sort.xml"}]