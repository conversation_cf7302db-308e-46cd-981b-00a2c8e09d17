[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_star_filled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_star_filled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_circle_number_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\circle_number_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_btn_crop_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\btn_crop_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_archive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_archive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_study_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_study_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_create_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_create_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_empty_deck.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_empty_deck.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_save_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_save_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_scissors.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_scissors.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_favorite_filled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_favorite_filled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_palette.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_palette.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_camera_overlay_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\camera_overlay_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_theme.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_theme.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\font_chenyu_luoyan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\font\\chenyu_luoyan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_simple_study.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_simple_study.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_bg_hint_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\bg_hint_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_expand_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_scrollbar_track.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\scrollbar_track.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\menu\\menu_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_star_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_star_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_batch_import.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_batch_import.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_deck_cover_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\deck_cover_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_volume_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_volume_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_circle_button_inward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\circle_button_inward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_folder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_sort.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_focus_indicator_capturing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\focus_indicator_capturing.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_ai_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_ai_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_circle_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\circle_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_chip_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\chip_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_bg_tag_rounded.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\bg_tag_rounded.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_gradient_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\gradient_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_font_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_font_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_crop_overlay_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_crop_overlay_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_focus_indicator_too_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\focus_indicator_too_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_card_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_card_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_deck_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_deck_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_font_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_font_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\font_noto_sans_tc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\font\\noto_sans_tc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_statistics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_clock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_clock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_simple_create_deck.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_simple_create_deck.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\font_font_family_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\font\\font_family_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_volume_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_volume_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\menu_menu_study_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\menu\\menu_study_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_circle_background_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\circle_background_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_create_deck.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_create_deck.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_filter_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_filter_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_create_deck_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_create_deck_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_card_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_card_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\color_bottom_nav_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\color\\bottom_nav_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_simple_deck.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_simple_deck.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\raw_chenyu_luoyan_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\raw\\chenyu_luoyan_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_focus_indicator_too_far.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\focus_indicator_too_far.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_book.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_book.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_template.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_template.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_search_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\search_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_icon_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_icon_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_font.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_font.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_quick_template.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_quick_template.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_font_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_font_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_btn_save_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\btn_save_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_camera.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_camera.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_lightbulb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_lightbulb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_item_font_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\item_font_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_export.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_export.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\menu_bottom_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\menu\\bottom_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_chart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_chart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_camera.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_camera.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_filter_inactive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_filter_inactive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_bg_card_content.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\bg_card_content.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_expand_less.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_expand_less.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_font_test_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\font_test_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_deck_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_deck_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\font_noto_sans_tc_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\font\\noto_sans_tc_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_dialog_card_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\dialog_card_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_ocr.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_ocr.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_photo_library.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_photo_library.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\layout_activity_photo_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\layout\\activity_photo_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_rotate_90.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_rotate_90.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_ai.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_ai.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_crop_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_crop_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_scrollbar_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\scrollbar_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\drawable_capture_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\drawable\\capture_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-debug-45:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.erroranalysis.app-main-47:\\xml\\data_extraction_rules.xml"}]