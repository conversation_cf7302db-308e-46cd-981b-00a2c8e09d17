<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- 1. 定義專門用於 Preference 的主題 -->
    <style name="AppTheme.PreferenceThemeOverlay" parent="@style/PreferenceThemeOverlay.v14.Material">
        <item name="android:fontFamily">@font/noto_sans_tc</item>
    </style>

    <!-- Base application theme. -->
    <style name="Theme.ErrorAnalysisApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_purple</item>
        <item name="colorOnPrimary">@color/text_white</item>

        <item name="colorSecondary">@color/success_green</item>
        <item name="colorSecondaryVariant">@color/info_blue</item>
        <item name="colorOnSecondary">@color/text_white</item>

        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <item name="colorSurface">@color/background_white</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <item name="colorError">@color/error_red</item>
        <item name="colorOnError">@color/text_white</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>

        <!-- 為 App 設定全域字體 -->
        <item name="android:fontFamily">@font/noto_sans_tc</item>

        <!-- 2. 在 App 主題中應用這個 Preference 主題 -->
        <item name="preferenceTheme">@style/AppTheme.PreferenceThemeOverlay</item>
    </style>
</resources>
