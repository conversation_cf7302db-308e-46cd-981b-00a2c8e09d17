<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="Theme.ErrorAnalysisApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_purple</item>
        <item name="colorOnPrimary">@color/text_white</item>

        <item name="colorSecondary">@color/success_green</item>
        <item name="colorSecondaryVariant">@color/info_blue</item>
        <item name="colorOnSecondary">@color/text_white</item>

        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <item name="colorSurface">@color/background_white</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <item name="colorError">@color/error_red</item>
        <item name="colorOnError">@color/text_white</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
    </style>
</resources>
