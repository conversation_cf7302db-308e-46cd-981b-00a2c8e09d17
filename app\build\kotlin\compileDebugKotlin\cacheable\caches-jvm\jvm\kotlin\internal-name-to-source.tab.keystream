.com/erroranalysis/app/ErrorAnalysisApplicationAcom/erroranalysis/app/ErrorAnalysisApplication$initializeOpenCV$18com/erroranalysis/app/ErrorAnalysisApplication$Companion*com/erroranalysis/app/data/DeckDataManager7com/erroranalysis/app/data/DeckDataManager$deleteDeck$17com/erroranalysis/app/data/DeckDataManager$deleteCard$24com/erroranalysis/app/data/DeckDataManager$Companion6com/erroranalysis/app/data/DeckDataManager$ContentItem@com/erroranalysis/app/data/DeckDataManager$imageStorageManager$2,com/erroranalysis/app/ui/base/ThemedActivity.com/erroranalysis/app/ui/camera/CameraActivityIcom/erroranalysis/app/ui/camera/CameraActivity$initializeAndStartCamera$1?com/erroranalysis/app/ui/camera/CameraActivity$setupObservers$1?com/erroranalysis/app/ui/camera/CameraActivity$setupObservers$2Lcom/erroranalysis/app/ui/camera/CameraActivity$setupObservers$2$WhenMappingsgcom/erroranalysis/app/ui/camera/CameraActivity$setupImageAnalyzers$focusAnalyzer$1$focusImageAnalyzer$1:com/erroranalysis/app/ui/camera/CameraActivity$takePhoto$18com/erroranalysis/app/ui/camera/CameraActivity$CompanionPcom/erroranalysis/app/ui/camera/CameraActivity$sam$androidx_lifecycle_Observer$0;com/erroranalysis/app/ui/camera/CameraActivity$WhenMappingsBcom/erroranalysis/app/ui/camera/CameraActivity$isReturnImageMode$2/com/erroranalysis/app/ui/camera/CameraViewModel<com/erroranalysis/app/ui/camera/CameraViewModel$CaptureState7com/erroranalysis/app/ui/camera/DocumentDetectionResult6com/erroranalysis/app/ui/camera/DocumentDetectionState5com/erroranalysis/app/ui/camera/CombinedImageAnalyzer?com/erroranalysis/app/ui/camera/CombinedImageAnalyzer$Companion7com/erroranalysis/app/ui/camera/CropOverlayTestActivity4com/erroranalysis/app/ui/camera/CropSelectionOverlay=com/erroranalysis/app/ui/camera/CropSelectionOverlay$DragModeAcom/erroranalysis/app/ui/camera/CropSelectionOverlay$WhenMappings7com/erroranalysis/app/ui/camera/DocumentBoundaryOverlayAcom/erroranalysis/app/ui/camera/DocumentBoundaryOverlay$CompanionDcom/erroranalysis/app/ui/camera/DocumentBoundaryOverlay$WhenMappings9com/erroranalysis/app/ui/camera/DocumentDetectionAnalyzerCcom/erroranalysis/app/ui/camera/DocumentDetectionAnalyzer$Companion-com/erroranalysis/app/ui/camera/FocusAnalyzer*com/erroranalysis/app/ui/camera/FocusState1com/erroranalysis/app/ui/camera/PhotoEditActivity;com/erroranalysis/app/ui/camera/PhotoEditActivity$Companion>com/erroranalysis/app/ui/camera/PhotoEditActivity$WhenMappingsEcom/erroranalysis/app/ui/camera/PhotoEditActivity$isReturnImageMode$24com/erroranalysis/app/ui/camera/SimpleCameraActivity@com/erroranalysis/app/ui/camera/SimpleCameraActivity$takePhoto$1>com/erroranalysis/app/ui/camera/SimpleCameraActivity$Companion0com/erroranalysis/app/ui/main/SimpleMainActivity;com/erroranalysis/app/ui/main/SimpleMainActivity$onCreate$12com/erroranalysis/app/ui/settings/SettingsActivityHcom/erroranalysis/app/ui/settings/SettingsActivity$setupThemeSelection$1Lcom/erroranalysis/app/ui/settings/SettingsActivity$showFontSelector$dialog$17com/erroranalysis/app/ui/settings/adapters/ThemeAdapterGcom/erroranalysis/app/ui/settings/adapters/ThemeAdapter$ThemeViewHolder2com/erroranalysis/app/ui/study/BatchImportActivityGcom/erroranalysis/app/ui/study/BatchImportActivity$importQuestionBank$1/com/erroranalysis/app/ui/study/CardEditActivity?com/erroranalysis/app/ui/study/CardEditActivity$testGeminiAPI$1Bcom/erroranalysis/app/ui/study/CardEditActivity$generateAIAnswer$19com/erroranalysis/app/ui/study/CardEditActivity$Companion)com/erroranalysis/app/ui/study/CardFilter+com/erroranalysis/app/ui/study/FilterResult1com/erroranalysis/app/ui/study/CardViewerActivityGcom/erroranalysis/app/ui/study/CardViewerActivity$speakCurrentContent$1;com/erroranalysis/app/ui/study/CardViewerActivity$Companion1com/erroranalysis/app/ui/study/DeckDetailActivityEcom/erroranalysis/app/ui/study/DeckDetailActivity$setupRecyclerView$1Ecom/erroranalysis/app/ui/study/DeckDetailActivity$setupRecyclerView$2Ecom/erroranalysis/app/ui/study/DeckDetailActivity$setupRecyclerView$3Vcom/erroranalysis/app/ui/study/DeckDetailActivity$setupSwipeToDelete$itemTouchHelper$1Ecom/erroranalysis/app/ui/study/DeckDetailActivity$performDeleteCard$1Wcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortByDescending$1Mcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortBy$1Zcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$compareByDescending$1Wcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$thenByDescending$1Wcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortByDescending$2Mcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortBy$2Wcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortByDescending$3Mcom/erroranalysis/app/ui/study/DeckDetailActivity$sortCards$$inlined$sortBy$3Ecom/erroranalysis/app/ui/study/DeckDetailActivity$confirmMoveCard$1$1;com/erroranalysis/app/ui/study/DeckDetailActivity$Companion>com/erroranalysis/app/ui/study/DeckDetailActivity$WhenMappings+com/erroranalysis/app/ui/study/CardSortType)com/erroranalysis/app/ui/study/DeckFilter+com/erroranalysis/app/ui/study/DeckSortType2com/erroranalysis/app/ui/study/SimpleStudyActivityFcom/erroranalysis/app/ui/study/SimpleStudyActivity$setupRecyclerView$1Fcom/erroranalysis/app/ui/study/SimpleStudyActivity$setupRecyclerView$2Gcom/erroranalysis/app/ui/study/SimpleStudyActivity$setupSearchAndSort$1Xcom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortByDescending$1Ncom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortBy$1[com/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$compareByDescending$1Xcom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$thenByDescending$1Ncom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortBy$2Xcom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortByDescending$2Xcom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortByDescending$3Ncom/erroranalysis/app/ui/study/SimpleStudyActivity$sortDecks$$inlined$sortBy$3Ucom/erroranalysis/app/ui/study/SimpleStudyActivity$showCreateDeckDialog$iconAdapter$1Icom/erroranalysis/app/ui/study/SimpleStudyActivity$editDeck$iconAdapter$1Acom/erroranalysis/app/ui/study/SimpleStudyActivity$deleteDeck$1$1<com/erroranalysis/app/ui/study/SimpleStudyActivity$Companion?com/erroranalysis/app/ui/study/SimpleStudyActivity$WhenMappings)com/erroranalysis/app/ui/study/SimpleDeck1com/erroranalysis/app/ui/study/SimpleDeck$Creator(com/erroranalysis/app/ui/study/StudyCard0com/erroranalysis/app/ui/study/StudyCard$Creator*com/erroranalysis/app/ui/study/CardMastery-com/erroranalysis/app/ui/study/CardDifficulty:com/erroranalysis/app/ui/study/CardDifficulty$WhenMappings+com/erroranalysis/app/ui/study/ReviewResult4com/erroranalysis/app/ui/study/adapters/ColorAdapterDcom/erroranalysis/app/ui/study/adapters/ColorAdapter$ColorViewHolderFcom/erroranalysis/app/ui/study/adapters/ColorAdapter$ColorDiffCallback3com/erroranalysis/app/ui/study/adapters/IconAdapterBcom/erroranalysis/app/ui/study/adapters/IconAdapter$IconViewHolderWcom/erroranalysis/app/ui/study/adapters/IconAdapter$IconViewHolder$bind$clickListener$10com/erroranalysis/app/ui/study/adapters/IconItem6com/erroranalysis/app/ui/study/adapters/IconCollection9com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapterHcom/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter$DeckViewHolderJcom/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter$DeckDiffCallback8com/erroranalysis/app/ui/study/adapters/StudyCardAdapterGcom/erroranalysis/app/ui/study/adapters/StudyCardAdapter$CardViewHolderDcom/erroranalysis/app/ui/study/adapters/StudyCardAdapter$ContentItemIcom/erroranalysis/app/ui/study/adapters/StudyCardAdapter$CardDiffCallback<com/erroranalysis/app/ui/test/DocumentCorrectionTestActivityGcom/erroranalysis/app/ui/test/DocumentCorrectionTestActivity$onCreate$1Fcom/erroranalysis/app/ui/test/DocumentCorrectionTestActivity$Companion'com/erroranalysis/app/ui/theme/AppTheme.com/erroranalysis/app/ui/theme/ThemeCollection0com/erroranalysis/app/ui/theme/FontDebugActivity/com/erroranalysis/app/ui/theme/FontExtensionsKt*com/erroranalysis/app/ui/theme/FontManager3com/erroranalysis/app/ui/theme/FontManager$FontType7com/erroranalysis/app/ui/theme/FontManager$WhenMappings2com/erroranalysis/app/ui/theme/FontSelectorAdapterAcom/erroranalysis/app/ui/theme/FontSelectorAdapter$FontViewHolder1com/erroranalysis/app/ui/theme/FontSelectorDialogEcom/erroranalysis/app/ui/theme/FontSelectorDialog$setupRecyclerView$1/com/erroranalysis/app/ui/theme/FontTestActivity.com/erroranalysis/app/ui/theme/FontTestAdapterAcom/erroranalysis/app/ui/theme/FontTestAdapter$FontTestViewHolder5com/erroranalysis/app/ui/theme/SimpleFontTestActivity+com/erroranalysis/app/ui/theme/ThemeManager5com/erroranalysis/app/ui/theme/ThemeManager$Companion?com/erroranalysis/app/ui/theme/ThemeManager$ThemeChangeListener)com/erroranalysis/app/ui/theme/ThemeUtils1com/erroranalysis/app/ui/widgets/RichTextEditText;com/erroranalysis/app/ui/widgets/RichTextEditText$Companion;com/erroranalysis/app/ui/widgets/RichTextEditText$ImageInfoCcom/erroranalysis/app/ui/widgets/RichTextEditText$ZoomableImageSpan=com/erroranalysis/app/ui/widgets/RichTextEditText$ContentItemGcom/erroranalysis/app/ui/widgets/RichTextEditText$imageStorageManager$2Ccom/erroranalysis/app/ui/widgets/RichTextEditText$gestureDetector$1.com/erroranalysis/app/utils/BatchImportManagerCcom/erroranalysis/app/utils/BatchImportManager$importQuestionBank$1>com/erroranalysis/app/utils/BatchImportManager$importFromZip$1Gcom/erroranalysis/app/utils/BatchImportManager$importMultipleCsvFiles$1Hcom/erroranalysis/app/utils/BatchImportManager$importFromCsvWithImages$1>com/erroranalysis/app/utils/BatchImportManager$importFromCsv$18com/erroranalysis/app/utils/BatchImportManager$Companion;com/erroranalysis/app/utils/BatchImportManager$WhenMappings(com/erroranalysis/app/utils/ImportResult0com/erroranalysis/app/utils/ImportResult$Success.com/erroranalysis/app/utils/ImportResult$Error4com/erroranalysis/app/utils/DocumentBoundaryDetectorTcom/erroranalysis/app/utils/DocumentBoundaryDetector$sortCorners$$inlined$sortedBy$1Tcom/erroranalysis/app/utils/DocumentBoundaryDetector$sortCorners$$inlined$sortedBy$2Tcom/erroranalysis/app/utils/DocumentBoundaryDetector$sortCorners$$inlined$sortedBy$3bcom/erroranalysis/app/utils/DocumentBoundaryDetector$detectHorizontalTextLines$$inlined$sortedBy$1>com/erroranalysis/app/utils/DocumentBoundaryDetector$Companion-com/erroranalysis/app/utils/DocumentProcessor7com/erroranalysis/app/utils/DocumentProcessor$Companion1com/erroranalysis/app/utils/DocumentProcessResult9com/erroranalysis/app/utils/DocumentProcessResult$Success7com/erroranalysis/app/utils/DocumentProcessResult$Error%com/erroranalysis/app/utils/ImageInfo+com/erroranalysis/app/utils/GeminiAIService;com/erroranalysis/app/utils/GeminiAIService$solveQuestion$2Jcom/erroranalysis/app/utils/GeminiAIService$solveQuestion$2$inputContent$1?com/erroranalysis/app/utils/GeminiAIService$testApiConnection$2Mcom/erroranalysis/app/utils/GeminiAIService$testApiConnection$2$testContent$15com/erroranalysis/app/utils/GeminiAIService$Companion=com/erroranalysis/app/utils/GeminiAIService$generativeModel$15com/erroranalysis/app/utils/GridSpacingItemDecoration/com/erroranalysis/app/utils/ImageStorageManager9com/erroranalysis/app/utils/ImageStorageManager$Companion;com/erroranalysis/app/utils/ImageStorageManager$imagesDir$2,com/erroranalysis/app/utils/MathFormatHelper%com/erroranalysis/app/utils/OCRHelper/com/erroranalysis/app/utils/OCRHelper$Companion-com/erroranalysis/app/utils/OpenCVInitializer)com/erroranalysis/app/utils/OpenCVManager0com/erroranalysis/app/utils/PerspectiveCorrector:com/erroranalysis/app/utils/PerspectiveCorrector$Companion                                                                                                                                                                                                                                                                                                                                                                                                                             