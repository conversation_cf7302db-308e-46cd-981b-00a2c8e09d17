// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFontOptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioButton radioFont;

  @NonNull
  public final TextView textFontName;

  @NonNull
  public final TextView textFontPreview;

  private ItemFontOptionBinding(@NonNull LinearLayout rootView, @NonNull RadioButton radioFont,
      @NonNull TextView textFontName, @NonNull TextView textFontPreview) {
    this.rootView = rootView;
    this.radioFont = radioFont;
    this.textFontName = textFontName;
    this.textFontPreview = textFontPreview;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFontOptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFontOptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_font_option, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFontOptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.radio_font;
      RadioButton radioFont = ViewBindings.findChildViewById(rootView, id);
      if (radioFont == null) {
        break missingId;
      }

      id = R.id.text_font_name;
      TextView textFontName = ViewBindings.findChildViewById(rootView, id);
      if (textFontName == null) {
        break missingId;
      }

      id = R.id.text_font_preview;
      TextView textFontPreview = ViewBindings.findChildViewById(rootView, id);
      if (textFontPreview == null) {
        break missingId;
      }

      return new ItemFontOptionBinding((LinearLayout) rootView, radioFont, textFontName,
          textFontPreview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
