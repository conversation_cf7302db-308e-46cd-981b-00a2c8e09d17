package com.erroranalysis.app.ui.study

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import com.erroranalysis.app.ui.base.ThemedActivity
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivityDeckDetailBinding
import com.erroranalysis.app.databinding.DialogCardFilterBinding
import com.erroranalysis.app.data.DeckDataManager
import com.erroranalysis.app.ui.study.adapters.StudyCardAdapter
import com.erroranalysis.app.ui.widgets.RichTextEditText
import com.google.android.material.chip.Chip

/**
 * 卡組詳細頁面
 * 顯示卡組內的所有卡片，支援卡片的CRUD操作
 */
class DeckDetailActivity : ThemedActivity() {

    private lateinit var binding: ActivityDeckDetailBinding
    private lateinit var cardAdapter: StudyCardAdapter
    private lateinit var dataManager: DeckDataManager
    private lateinit var deck: SimpleDeck
    private lateinit var deckId: String
    private val cardList = mutableListOf<StudyCard>()
    private val filteredCardList = mutableListOf<StudyCard>()
    private var currentFilter = CardFilter()
    private var currentSortType = CardSortType.CREATED_TIME_DESC

    companion object {
        const val EXTRA_DECK = "extra_deck"
        const val REQUEST_CREATE_CARD = 1001
        const val REQUEST_EDIT_CARD = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeckDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 獲取傳入的卡組資料
        deck = intent.getParcelableExtra(EXTRA_DECK) ?: run {
            finish()
            return
        }

        // 初始化deckId
        deckId = deck.id

        // 初始化資料管理器
        dataManager = DeckDataManager(this)

        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupFilterButton()
        loadCards()

        // 應用主題
        applyTheme()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayShowTitleEnabled(false) // 隱藏預設標題
            setDisplayHomeAsUpEnabled(true)
        }

        // 設置自定義標題
        binding.textDeckTitle.text = deck.name
        updateToolbarTitle()
    }

    private fun setupRecyclerView() {
        cardAdapter = StudyCardAdapter(
            onCardClick = { card -> viewCard(card) },
            onCardLongClick = { card -> showCardOptions(card) },
            onStarClick = { card -> toggleCardStar(card) }
        )

        binding.recyclerCards.apply {
            layoutManager = LinearLayoutManager(this@DeckDetailActivity)
            adapter = cardAdapter
        }

        // 設置滑動刪除功能
        setupSwipeToDelete()
    }

    private fun setupFab() {
        binding.fabCreateCard.setOnClickListener {
            showCreateCardDialog()
        }
    }

    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }

        binding.btnSort.setOnClickListener {
            showCardSortDialog()
        }
    }

    /**
     * 設置滑動刪除功能
     */
    private fun setupSwipeToDelete() {
        val itemTouchHelper = ItemTouchHelper(object : ItemTouchHelper.SimpleCallback(
            0, // 不支援拖拽
            ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT // 支援左右滑動
        ) {
            override fun onMove(
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                target: androidx.recyclerview.widget.RecyclerView.ViewHolder
            ): Boolean {
                return false // 不支援拖拽移動
            }

            override fun onSwiped(
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                direction: Int
            ) {
                val position = viewHolder.adapterPosition
                if (position != androidx.recyclerview.widget.RecyclerView.NO_POSITION) {
                    val card = filteredCardList[position]
                    showSwipeDeleteConfirmation(card, position)
                }
            }

            override fun getSwipeThreshold(viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder): Float {
                return 0.3f // 滑動30%就觸發
            }

            override fun onChildDraw(
                c: android.graphics.Canvas,
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                dX: Float,
                dY: Float,
                actionState: Int,
                isCurrentlyActive: Boolean
            ) {
                if (actionState == ItemTouchHelper.ACTION_STATE_SWIPE) {
                    // 繪製刪除背景
                    val itemView = viewHolder.itemView
                    val paint = android.graphics.Paint()

                    if (dX > 0) { // 向右滑動
                        paint.color = android.graphics.Color.parseColor("#F44336") // 紅色背景
                        c.drawRect(
                            itemView.left.toFloat(),
                            itemView.top.toFloat(),
                            dX,
                            itemView.bottom.toFloat(),
                            paint
                        )

                        // 繪製刪除圖標
                        val deleteIcon = androidx.core.content.ContextCompat.getDrawable(
                            this@DeckDetailActivity,
                            android.R.drawable.ic_menu_delete
                        )
                        deleteIcon?.let { icon ->
                            val iconSize = 64
                            val iconMargin = (itemView.height - iconSize) / 2
                            icon.setBounds(
                                itemView.left + iconMargin,
                                itemView.top + iconMargin,
                                itemView.left + iconMargin + iconSize,
                                itemView.bottom - iconMargin
                            )
                            icon.setTint(android.graphics.Color.WHITE)
                            icon.draw(c)
                        }
                    } else if (dX < 0) { // 向左滑動
                        paint.color = android.graphics.Color.parseColor("#F44336") // 紅色背景
                        c.drawRect(
                            itemView.right.toFloat() + dX,
                            itemView.top.toFloat(),
                            itemView.right.toFloat(),
                            itemView.bottom.toFloat(),
                            paint
                        )

                        // 繪製刪除圖標
                        val deleteIcon = androidx.core.content.ContextCompat.getDrawable(
                            this@DeckDetailActivity,
                            android.R.drawable.ic_menu_delete
                        )
                        deleteIcon?.let { icon ->
                            val iconSize = 64
                            val iconMargin = (itemView.height - iconSize) / 2
                            icon.setBounds(
                                itemView.right - iconMargin - iconSize,
                                itemView.top + iconMargin,
                                itemView.right - iconMargin,
                                itemView.bottom - iconMargin
                            )
                            icon.setTint(android.graphics.Color.WHITE)
                            icon.draw(c)
                        }
                    }
                }

                super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
            }
        })

        itemTouchHelper.attachToRecyclerView(binding.recyclerCards)
    }

    private fun loadCards() {
        cardList.clear()
        val loadedCards = dataManager.loadCardsByDeckId(deck.id)

        android.util.Log.d("DeckDetail", "載入卡組 ${deck.id} 的卡片，數量: ${loadedCards.size}")

        // 驗證所有卡片都屬於當前卡組
        val validCards = loadedCards.filter { it.deckId == deck.id }
        if (validCards.size != loadedCards.size) {
            android.util.Log.w("DeckDetail", "發現 ${loadedCards.size - validCards.size} 張不屬於當前卡組的卡片")
        }

        cardList.addAll(validCards)
        applyFilter()
        updateEmptyState()
        updateToolbarSubtitle()
    }

    private fun showCreateCardDialog() {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        startActivityForResult(intent, REQUEST_CREATE_CARD)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_CREATE_CARD, REQUEST_EDIT_CARD -> {
                    val cardSaved = data?.getBooleanExtra(CardEditActivity.RESULT_CARD_SAVED, false) ?: false
                    if (cardSaved) {
                        // 重新載入卡片列表
                        loadCards()
                    }
                }
            }
        }
    }

    private fun viewCard(card: StudyCard) {
        android.util.Log.e("DeckDetail", "=== 開始viewCard ===")
        android.util.Log.e("DeckDetail", "準備啟動CardViewActivity")
        android.util.Log.e("DeckDetail", "卡片ID: ${card.id}")
        android.util.Log.e("DeckDetail", "卡組ID: $deckId")
        android.util.Log.e("DeckDetail", "卡片題目: ${card.question.take(50)}")

        // 添加Toast提示確認點擊事件觸發
        android.widget.Toast.makeText(this, "正在開啟卡片：${card.id}", android.widget.Toast.LENGTH_SHORT).show()

        try {
            // 啟動全新的卡片檢視Activity
            val intent = Intent(this, CardViewerActivity::class.java)

            // 驗證Intent創建
            android.util.Log.e("DeckDetail", "Intent創建成功")

            // 添加數據
            intent.putExtra(CardViewerActivity.EXTRA_CARD, card)
            intent.putExtra(CardViewerActivity.EXTRA_DECK_ID, deckId)
            android.util.Log.e("DeckDetail", "Intent數據添加完成")

            // 驗證Activity類是否存在
            val componentName = intent.component
            android.util.Log.e("DeckDetail", "目標Activity: ${componentName?.className}")

            android.util.Log.e("DeckDetail", "準備調用startActivity")
            startActivity(intent)
            android.util.Log.e("DeckDetail", "startActivity調用完成")

        } catch (e: Exception) {
            android.util.Log.e("DeckDetail", "啟動CardViewActivity失敗", e)
            android.widget.Toast.makeText(this, "無法開啟卡片檢視：${e.message}", android.widget.Toast.LENGTH_LONG).show()
        }

        android.util.Log.e("DeckDetail", "=== viewCard結束 ===")
    }

    // 舊的對話框方法已刪除，現在使用CardViewActivity

    private fun showCardOptions(card: StudyCard) {
        val options = arrayOf("編輯", "移動", "刪除")

        AlertDialog.Builder(this)
            .setTitle("卡片選項")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> editCard(card)
                    1 -> moveCard(card)
                    2 -> deleteCard(card)
                }
            }
            .show()
    }

    private fun editCard(card: StudyCard) {
        val intent = Intent(this, CardEditActivity::class.java)
        intent.putExtra(CardEditActivity.EXTRA_DECK_ID, deck.id)
        intent.putExtra(CardEditActivity.EXTRA_CARD, card)
        startActivityForResult(intent, REQUEST_EDIT_CARD)
    }

    private fun moveCard(card: StudyCard) {
        // 獲取所有卡組，排除當前卡組
        val allDecks = dataManager.loadDecks()
        val targetDecks = allDecks.filter { it.id != deckId }

        if (targetDecks.isEmpty()) {
            AlertDialog.Builder(this)
                .setTitle("無法移動")
                .setMessage("沒有其他卡組可以移動到")
                .setPositiveButton("確定", null)
                .show()
            return
        }

        // 創建卡組選項
        val deckNames = targetDecks.map { "${it.icon} ${it.name}" }.toTypedArray()

        AlertDialog.Builder(this)
            .setTitle("移動卡片到")
            .setItems(deckNames) { _, which ->
                val targetDeck = targetDecks[which]
                confirmMoveCard(card, targetDeck)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun confirmMoveCard(card: StudyCard, targetDeck: SimpleDeck) {
        AlertDialog.Builder(this)
            .setTitle("確認移動")
            .setMessage("確定要將此卡片移動到「${targetDeck.name}」嗎？")
            .setPositiveButton("移動") { _, _ ->
                // 執行移動
                dataManager.moveCard(card.id, targetDeck.id)

                // 更新UI
                cardList.removeAll { it.id == card.id }
                cardAdapter.submitList(cardList.toList())
                updateEmptyState()
                updateToolbarSubtitle()

                // 顯示成功訊息
                Toast.makeText(this, "卡片已移動到「${targetDeck.name}」", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun toggleCardStar(card: StudyCard) {
        // 切換星星狀態
        dataManager.toggleCardStar(card.id)

        // 更新本地列表
        val index = cardList.indexOfFirst { it.id == card.id }
        if (index != -1) {
            cardList[index] = card.copy(isStarred = !card.isStarred)
            cardAdapter.submitList(cardList.toList())
        }
    }

    /**
     * 顯示滑動刪除確認對話框
     */
    private fun showSwipeDeleteConfirmation(card: StudyCard, position: Int) {
        AlertDialog.Builder(this)
            .setTitle("刪除卡片")
            .setMessage("確定要刪除這張卡片嗎？")
            .setPositiveButton("刪除") { _, _ ->
                performDeleteCard(card)
            }
            .setNegativeButton("取消") { _, _ ->
                // 取消刪除，恢復卡片位置
                cardAdapter.notifyItemChanged(position)
            }
            .setOnCancelListener {
                // 對話框被取消，恢復卡片位置
                cardAdapter.notifyItemChanged(position)
            }
            .show()
    }

    private fun deleteCard(card: StudyCard) {
        AlertDialog.Builder(this)
            .setTitle("刪除卡片")
            .setMessage("確定要刪除這張卡片嗎？")
            .setPositiveButton("刪除") { _, _ ->
                performDeleteCard(card)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 執行刪除卡片操作
     */
    private fun performDeleteCard(card: StudyCard) {
        // 從資料管理器刪除
        dataManager.deleteCard(card.id)

        // 更新UI
        cardList.removeAll { it.id == card.id }
        applyFilter() // 重新應用篩選和排序
        updateEmptyState()
        updateToolbarSubtitle()

        // 顯示刪除成功訊息
        Toast.makeText(this, "卡片已刪除", Toast.LENGTH_SHORT).show()
    }

    private fun updateEmptyState() {
        if (cardList.isEmpty()) {
            binding.layoutEmpty.visibility = android.view.View.VISIBLE
            binding.recyclerCards.visibility = android.view.View.GONE
        } else {
            binding.layoutEmpty.visibility = android.view.View.GONE
            binding.recyclerCards.visibility = android.view.View.VISIBLE
        }
    }

    private fun updateToolbarTitle() {
        binding.textCardCount.text = cardList.size.toString()
    }

    private fun updateToolbarSubtitle() {
        // 保留此方法以防其他地方調用
        updateToolbarTitle()
    }

    override fun onResume() {
        super.onResume()
        // 重新載入資料，確保資料同步
        loadCards()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    /**
     * 顯示篩選對話框
     */
    private fun showFilterDialog() {
        val dialogBinding = DialogCardFilterBinding.inflate(layoutInflater)

        // 設置當前篩選條件
        dialogBinding.editKeyword.setText(currentFilter.keyword)

        // 設置熟練度選項
        setupMasteryChips(dialogBinding)

        // 設置星星篩選選項
        setupStarChips(dialogBinding)

        // 設置標籤選項
        setupTagChips(dialogBinding)

        val dialog = AlertDialog.Builder(this)
            .setView(dialogBinding.root)
            .create()

        // 清除按鈕
        dialogBinding.btnClear.setOnClickListener {
            currentFilter = CardFilter()
            applyFilter()
            dialog.dismiss()
        }

        // 套用按鈕
        dialogBinding.btnApply.setOnClickListener {
            val keyword = dialogBinding.editKeyword.text.toString().trim()
            val selectedMastery = getSelectedMasteryLevels(dialogBinding)
            val selectedTags = getSelectedTags(dialogBinding)
            val (showStarred, showNotStarred) = getSelectedStarOptions(dialogBinding)

            currentFilter = CardFilter(keyword, selectedMastery, selectedTags, showStarred, showNotStarred)
            applyFilter()
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 設置熟練度選項
     */
    private fun setupMasteryChips(dialogBinding: DialogCardFilterBinding) {
        val masteryChips = mapOf(
            dialogBinding.chipLevel1 to CardMastery.LEVEL_1,
            dialogBinding.chipLevel2 to CardMastery.LEVEL_2,
            dialogBinding.chipLevel3 to CardMastery.LEVEL_3,
            dialogBinding.chipLevel4 to CardMastery.LEVEL_4,
            dialogBinding.chipLevel5 to CardMastery.LEVEL_5
        )

        masteryChips.forEach { (chip, mastery) ->
            chip.isChecked = currentFilter.selectedMasteryLevels.contains(mastery)
        }
    }

    /**
     * 設置星星篩選選項
     */
    private fun setupStarChips(dialogBinding: DialogCardFilterBinding) {
        dialogBinding.chipStarred.isChecked = currentFilter.showStarred
        dialogBinding.chipNotStarred.isChecked = currentFilter.showNotStarred
    }

    /**
     * 設置標籤選項
     */
    private fun setupTagChips(dialogBinding: DialogCardFilterBinding) {
        val allTags = cardList.flatMap { it.tags }.distinct().sorted()

        dialogBinding.chipGroupTags.removeAllViews()

        allTags.forEach { tag ->
            val chip = Chip(this)
            chip.text = tag
            chip.isCheckable = true
            chip.isChecked = currentFilter.selectedTags.contains(tag)
            dialogBinding.chipGroupTags.addView(chip)
        }
    }

    /**
     * 獲取選中的熟練度
     */
    private fun getSelectedMasteryLevels(dialogBinding: DialogCardFilterBinding): Set<CardMastery> {
        val selected = mutableSetOf<CardMastery>()

        if (dialogBinding.chipLevel1.isChecked) selected.add(CardMastery.LEVEL_1)
        if (dialogBinding.chipLevel2.isChecked) selected.add(CardMastery.LEVEL_2)
        if (dialogBinding.chipLevel3.isChecked) selected.add(CardMastery.LEVEL_3)
        if (dialogBinding.chipLevel4.isChecked) selected.add(CardMastery.LEVEL_4)
        if (dialogBinding.chipLevel5.isChecked) selected.add(CardMastery.LEVEL_5)

        return selected
    }

    /**
     * 獲取選中的星星篩選選項
     */
    private fun getSelectedStarOptions(dialogBinding: DialogCardFilterBinding): Pair<Boolean, Boolean> {
        return Pair(
            dialogBinding.chipStarred.isChecked,
            dialogBinding.chipNotStarred.isChecked
        )
    }

    /**
     * 獲取選中的標籤
     */
    private fun getSelectedTags(dialogBinding: DialogCardFilterBinding): Set<String> {
        val selected = mutableSetOf<String>()

        for (i in 0 until dialogBinding.chipGroupTags.childCount) {
            val chip = dialogBinding.chipGroupTags.getChildAt(i) as Chip
            if (chip.isChecked) {
                selected.add(chip.text.toString())
            }
        }

        return selected
    }

    /**
     * 套用篩選條件和排序
     */
    private fun applyFilter() {
        filteredCardList.clear()

        if (currentFilter.hasFilter()) {
            filteredCardList.addAll(cardList.filter { currentFilter.matches(it) })
        } else {
            filteredCardList.addAll(cardList)
        }

        // 應用排序
        sortCards()

        cardAdapter.submitList(filteredCardList.toList())
        updateFilterIndicator()
    }

    /**
     * 排序卡片
     */
    private fun sortCards() {
        when (currentSortType) {
            CardSortType.CREATED_TIME_DESC -> filteredCardList.sortByDescending { it.createdTime }
            CardSortType.CREATED_TIME_ASC -> filteredCardList.sortBy { it.createdTime }
            CardSortType.STARRED_FIRST -> filteredCardList.sortWith(compareByDescending<StudyCard> { it.isStarred }.thenByDescending { it.createdTime })
            CardSortType.MASTERY_DESC -> filteredCardList.sortByDescending { it.masteryLevel }
            CardSortType.MASTERY_ASC -> filteredCardList.sortBy { it.masteryLevel }
            CardSortType.REVIEW_COUNT_DESC -> filteredCardList.sortByDescending { it.reviewCount }
            CardSortType.REVIEW_COUNT_ASC -> filteredCardList.sortBy { it.reviewCount }
        }
    }

    /**
     * 顯示卡片排序對話框
     */
    private fun showCardSortDialog() {
        val sortOptions = CardSortType.values().map { it.displayName }.toTypedArray()
        val currentIndex = CardSortType.values().indexOf(currentSortType)

        AlertDialog.Builder(this)
            .setTitle("排序方式")
            .setSingleChoiceItems(sortOptions, currentIndex) { dialog, which ->
                currentSortType = CardSortType.values()[which]
                applyFilter()
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 更新篩選指示器
     */
    private fun updateFilterIndicator() {
        if (currentFilter.hasFilter()) {
            // 有篩選時：使用藍色圖標，顯示篩選數/總數
            binding.btnFilter.setImageResource(R.drawable.ic_filter_active)
            binding.textCardCount.text = "${filteredCardList.size}/${cardList.size}"
        } else {
            // 無篩選時：使用白色圖標，顯示總數
            binding.btnFilter.setImageResource(R.drawable.ic_filter_inactive)
            binding.textCardCount.text = cardList.size.toString()
        }
    }

    override fun onApplyTheme(theme: AppTheme) {
        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(theme.getBackgroundColorInt())

        // 應用主題到工具欄
        binding.toolbar.setBackgroundColor(theme.getPrimaryColorInt())

        // 應用主題到FAB
        binding.fabCreateCard.backgroundTintList =
            android.content.res.ColorStateList.valueOf(theme.getPrimaryColorInt())
    }
}

/**
 * 卡片排序類型
 */
enum class CardSortType(val displayName: String) {
    CREATED_TIME_DESC("創建時間（新到舊）"),
    CREATED_TIME_ASC("創建時間（舊到新）"),
    STARRED_FIRST("重要優先"),
    MASTERY_DESC("熟練度（高到低）"),
    MASTERY_ASC("熟練度（低到高）"),
    REVIEW_COUNT_DESC("複習次數（多到少）"),
    REVIEW_COUNT_ASC("複習次數（少到多）")
}
