.com.erroranalysis.app.ErrorAnalysisApplication,com.erroranalysis.app.ui.base.ThemedActivity.com.erroranalysis.app.ui.camera.CameraActivity/com.erroranalysis.app.ui.camera.CameraViewModel<com.erroranalysis.app.ui.camera.CameraViewModel.CaptureState6com.erroranalysis.app.ui.camera.DocumentDetectionState5com.erroranalysis.app.ui.camera.CombinedImageAnalyzer7com.erroranalysis.app.ui.camera.CropOverlayTestActivity4com.erroranalysis.app.ui.camera.CropSelectionOverlay=com.erroranalysis.app.ui.camera.CropSelectionOverlay.DragMode7com.erroranalysis.app.ui.camera.DocumentBoundaryOverlay9com.erroranalysis.app.ui.camera.DocumentDetectionAnalyzer-com.erroranalysis.app.ui.camera.FocusAnalyzer*com.erroranalysis.app.ui.camera.FocusState1com.erroranalysis.app.ui.camera.PhotoEditActivity4com.erroranalysis.app.ui.camera.SimpleCameraActivity0com.erroranalysis.app.ui.main.SimpleMainActivity2com.erroranalysis.app.ui.settings.SettingsActivity7com.erroranalysis.app.ui.settings.adapters.ThemeAdapterGcom.erroranalysis.app.ui.settings.adapters.ThemeAdapter.ThemeViewHolder2com.erroranalysis.app.ui.study.BatchImportActivity/com.erroranalysis.app.ui.study.CardEditActivity1com.erroranalysis.app.ui.study.CardViewerActivity1com.erroranalysis.app.ui.study.DeckDetailActivity+com.erroranalysis.app.ui.study.CardSortType+com.erroranalysis.app.ui.study.DeckSortType2com.erroranalysis.app.ui.study.SimpleStudyActivity)com.erroranalysis.app.ui.study.SimpleDeck(com.erroranalysis.app.ui.study.StudyCard*com.erroranalysis.app.ui.study.CardMastery-com.erroranalysis.app.ui.study.CardDifficulty+com.erroranalysis.app.ui.study.ReviewResult4com.erroranalysis.app.ui.study.adapters.ColorAdapterDcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorViewHolderFcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorDiffCallback3com.erroranalysis.app.ui.study.adapters.IconAdapterBcom.erroranalysis.app.ui.study.adapters.IconAdapter.IconViewHolder9com.erroranalysis.app.ui.study.adapters.SimpleDeckAdapterHcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckViewHolderJcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckDiffCallback8com.erroranalysis.app.ui.study.adapters.StudyCardAdapterGcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardViewHolderIcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardDiffCallback<com.erroranalysis.app.ui.test.DocumentCorrectionTestActivity0com.erroranalysis.app.ui.theme.FontDebugActivity3com.erroranalysis.app.ui.theme.FontManager.FontType2com.erroranalysis.app.ui.theme.FontSelectorAdapterAcom.erroranalysis.app.ui.theme.FontSelectorAdapter.FontViewHolder1com.erroranalysis.app.ui.theme.FontSelectorDialog/com.erroranalysis.app.ui.theme.FontTestActivity.com.erroranalysis.app.ui.theme.FontTestAdapterAcom.erroranalysis.app.ui.theme.FontTestAdapter.FontTestViewHolder5com.erroranalysis.app.ui.theme.SimpleFontTestActivity1com.erroranalysis.app.ui.widgets.RichTextEditTextCcom.erroranalysis.app.ui.widgets.RichTextEditText.ZoomableImageSpan0com.erroranalysis.app.utils.ImportResult.Success.com.erroranalysis.app.utils.ImportResult.Error9com.erroranalysis.app.utils.DocumentProcessResult.Success7com.erroranalysis.app.utils.DocumentProcessResult.Error5com.erroranalysis.app.utils.GridSpacingItemDecoration2com.erroranalysis.app.databinding.ItemThemeBinding9com.erroranalysis.app.databinding.ActivityCardEditBinding6com.erroranalysis.app.databinding.ItemStudyCardBinding9com.erroranalysis.app.databinding.ActivitySettingsBinding2com.erroranalysis.app.databinding.ItemColorBinding;com.erroranalysis.app.databinding.ActivityDeckDetailBinding<com.erroranalysis.app.databinding.ActivityBatchImportBinding<com.erroranalysis.app.databinding.ActivitySimpleStudyBinding7com.erroranalysis.app.databinding.ItemFontOptionBinding9com.erroranalysis.app.databinding.DialogCardFilterBinding;com.erroranalysis.app.databinding.DialogFontSelectorBinding7com.erroranalysis.app.databinding.ActivityCameraBinding9com.erroranalysis.app.databinding.ItemIconSelectorBinding7com.erroranalysis.app.databinding.ItemSimpleDeckBinding;com.erroranalysis.app.databinding.ActivityCardViewerBinding5com.erroranalysis.app.databinding.ActivityMainBinding9com.erroranalysis.app.databinding.ActivityFontTestBinding:com.erroranalysis.app.databinding.ActivityPhotoEditBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           