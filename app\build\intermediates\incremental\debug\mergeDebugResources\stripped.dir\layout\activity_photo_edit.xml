<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/camera_background"
    tools:context=".ui.camera.PhotoEditActivity">

    <!-- 頂部工具欄 -->
    <LinearLayout
        android:id="@+id/layout_top_toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/camera_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按鈕 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_button_background"
            android:contentDescription="返回"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/text_white" />

        <!-- 標題 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="選擇處理方式"
            android:textColor="@color/text_white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 重置按鈕 -->
        <ImageButton
            android:id="@+id/btn_reset"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_button_background"
            android:contentDescription="重置"
            android:src="@drawable/ic_settings"
            android:tint="@color/text_white" />

    </LinearLayout>

    <!-- 照片顯示區域 -->
    <FrameLayout
        android:id="@+id/layout_photo_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom_controls"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top_toolbar">

        <!-- 照片 ImageView -->
        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            tools:src="@drawable/ic_camera" />

        <!-- 裁剪選擇框覆蓋層 -->
        <com.erroranalysis.app.ui.camera.CropSelectionOverlay
            android:id="@+id/crop_selection_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!-- 文檔邊界調整覆蓋層 -->
        <com.erroranalysis.app.ui.camera.DocumentBoundaryOverlay
            android:id="@+id/document_boundary_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>

    <!-- 底部控制區域 -->
    <LinearLayout
        android:id="@+id/layout_bottom_controls"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@color/camera_background"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="32dp"
        android:paddingTop="8dp"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 說明文字 -->
        <TextView
            android:id="@+id/tv_instruction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="選擇處理方式"
            android:textColor="@color/text_white"
            android:textSize="14sp" />

        <!-- 選擇模式按鈕區域 -->
        <LinearLayout
            android:id="@+id/layout_selection_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">



            <!-- 手動調整按鈕 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal">

                <!-- 梯形修正 -->
                <Button
                    android:id="@+id/btn_perspective_correction"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/circle_button_background"
                    android:text="梯形修正"
                    android:textColor="@color/text_white"
                    android:textSize="14sp" />

                <!-- 裁切調整 -->
                <Button
                    android:id="@+id/btn_crop_adjustment"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/circle_button_background"
                    android:text="裁切調整"
                    android:textColor="@color/text_white"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 處理按鈕區域（統一樣式的圓形按鈕：保存、去筆跡、裁切） -->
        <LinearLayout
            android:id="@+id/layout_confirm_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:visibility="visible">

            <!-- 保存按鈕 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:orientation="vertical"
                android:gravity="center">

                <ImageButton
                    android:id="@+id/btn_cancel"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:background="@drawable/circle_button_background"
                    android:src="@drawable/ic_save_circle"
                    android:contentDescription="保存"
                    android:scaleType="center"
                    android:tint="@color/text_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="保存"
                    android:textColor="@color/text_white"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- 去筆跡按鈕 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:orientation="vertical"
                android:gravity="center">

                <ImageButton
                    android:id="@+id/btn_confirm"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:background="@drawable/circle_button_background"
                    android:src="@drawable/ic_edit"
                    android:contentDescription="去筆跡"
                    android:scaleType="center"
                    android:tint="@color/text_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="去筆跡"
                    android:textColor="@color/text_white"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- 裁切按鈕 -->
            <ImageButton
                android:id="@+id/btn_crop"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginHorizontal="24dp"
                android:background="@drawable/circle_button_background"
                android:src="@drawable/ic_scissors"
                android:contentDescription="裁切"
                android:scaleType="center"
                android:tint="@color/text_white" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
