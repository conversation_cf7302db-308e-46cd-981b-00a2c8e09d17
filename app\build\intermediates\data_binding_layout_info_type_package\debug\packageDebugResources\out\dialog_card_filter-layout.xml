<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_card_filter" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\dialog_card_filter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_card_filter_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="176" endOffset="14"/></Target><Target id="@+id/edit_keyword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="59"/></Target><Target id="@+id/chip_group_mastery" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="47" startOffset="4" endLine="95" endOffset="48"/></Target><Target id="@+id/chip_level1" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="55" startOffset="8" endLine="61" endOffset="53"/></Target><Target id="@+id/chip_level2" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="63" startOffset="8" endLine="69" endOffset="53"/></Target><Target id="@+id/chip_level3" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="71" startOffset="8" endLine="77" endOffset="53"/></Target><Target id="@+id/chip_level4" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="79" startOffset="8" endLine="85" endOffset="53"/></Target><Target id="@+id/chip_level5" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="87" startOffset="8" endLine="93" endOffset="53"/></Target><Target id="@+id/chip_group_star" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="107" startOffset="4" endLine="131" endOffset="48"/></Target><Target id="@+id/chip_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="115" startOffset="8" endLine="121" endOffset="53"/></Target><Target id="@+id/chip_not_starred" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="123" startOffset="8" endLine="129" endOffset="53"/></Target><Target id="@+id/chip_group_tags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="143" startOffset="4" endLine="149" endOffset="37"/></Target><Target id="@+id/btn_clear" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="158" startOffset="8" endLine="165" endOffset="55"/></Target><Target id="@+id/btn_apply" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="167" startOffset="8" endLine="172" endOffset="58"/></Target></Targets></Layout>