<app/src/main/java/com/erroranalysis/app/ui/theme/AppTheme.ktDapp/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.ktEapp/src/main/java/com/erroranalysis/app/ui/main/SimpleMainActivity.ktFapp/src/main/java/com/erroranalysis/app/ui/study/CardViewerActivity.ktHapp/src/main/java/com/erroranalysis/app/ui/study/adapters/IconAdapter.ktBapp/src/main/java/com/erroranalysis/app/ui/camera/FocusAnalyzer.ktGapp/src/main/java/com/erroranalysis/app/ui/settings/SettingsActivity.ktQapp/src/main/java/com/erroranalysis/app/ui/test/DocumentCorrectionTestActivity.kt@app/src/main/java/com/erroranalysis/app/ui/theme/ThemeManager.ktIapp/src/main/java/com/erroranalysis/app/ui/camera/CropSelectionOverlay.ktEapp/src/main/java/com/erroranalysis/app/utils/PerspectiveCorrector.ktGapp/src/main/java/com/erroranalysis/app/ui/study/BatchImportActivity.ktMapp/src/main/java/com/erroranalysis/app/ui/study/adapters/StudyCardAdapter.ktBapp/src/main/java/com/erroranalysis/app/ui/theme/FontExtensions.ktGapp/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorAdapter.ktBapp/src/main/java/com/erroranalysis/app/utils/OpenCVInitializer.ktNapp/src/main/java/com/erroranalysis/app/ui/study/adapters/SimpleDeckAdapter.ktLapp/src/main/java/com/erroranalysis/app/ui/camera/CropOverlayTestActivity.kt?app/src/main/java/com/erroranalysis/app/data/DeckDataManager.kt=app/src/main/java/com/erroranalysis/app/ui/study/StudyCard.kt>app/src/main/java/com/erroranalysis/app/ui/study/DeckFilter.ktAapp/src/main/java/com/erroranalysis/app/ui/base/ThemedActivity.ktIapp/src/main/java/com/erroranalysis/app/utils/DocumentBoundaryDetector.ktGapp/src/main/java/com/erroranalysis/app/ui/study/SimpleStudyActivity.ktJapp/src/main/java/com/erroranalysis/app/ui/theme/SimpleFontTestActivity.ktFapp/src/main/java/com/erroranalysis/app/ui/theme/FontSelectorDialog.ktJapp/src/main/java/com/erroranalysis/app/ui/camera/CombinedImageAnalyzer.kt:app/src/main/java/com/erroranalysis/app/utils/OCRHelper.ktDapp/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.kt@app/src/main/java/com/erroranalysis/app/utils/GeminiAIService.ktLapp/src/main/java/com/erroranalysis/app/ui/settings/adapters/ThemeAdapter.ktFapp/src/main/java/com/erroranalysis/app/ui/camera/PhotoEditActivity.ktAapp/src/main/java/com/erroranalysis/app/utils/MathFormatHelper.ktFapp/src/main/java/com/erroranalysis/app/ui/study/DeckDetailActivity.ktFapp/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt>app/src/main/java/com/erroranalysis/app/ui/study/CardFilter.ktBapp/src/main/java/com/erroranalysis/app/utils/DocumentProcessor.ktCapp/src/main/java/com/erroranalysis/app/utils/BatchImportManager.ktJapp/src/main/java/com/erroranalysis/app/utils/GridSpacingItemDecoration.ktCapp/src/main/java/com/erroranalysis/app/ui/camera/CameraActivity.ktCapp/src/main/java/com/erroranalysis/app/ErrorAnalysisApplication.kt?app/src/main/java/com/erroranalysis/app/ui/theme/FontManager.ktIapp/src/main/java/com/erroranalysis/app/ui/study/adapters/ColorAdapter.kt>app/src/main/java/com/erroranalysis/app/utils/OpenCVManager.ktDapp/src/main/java/com/erroranalysis/app/ui/theme/FontTestActivity.ktLapp/src/main/java/com/erroranalysis/app/ui/camera/DocumentBoundaryOverlay.ktDapp/src/main/java/com/erroranalysis/app/ui/camera/CameraViewModel.ktIapp/src/main/java/com/erroranalysis/app/ui/camera/SimpleCameraActivity.ktNapp/src/main/java/com/erroranalysis/app/ui/camera/DocumentDetectionAnalyzer.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     