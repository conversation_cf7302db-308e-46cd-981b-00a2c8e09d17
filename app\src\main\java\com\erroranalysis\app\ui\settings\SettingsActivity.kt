package com.erroranalysis.app.ui.settings

import android.animation.ValueAnimator
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.erroranalysis.app.R
import com.erroranalysis.app.databinding.ActivitySettingsBinding
import com.erroranalysis.app.ui.theme.ThemeManager
import com.erroranalysis.app.ui.theme.AppTheme
import com.erroranalysis.app.ui.theme.FontManager
import com.erroranalysis.app.ui.theme.FontSelectorDialog
import com.erroranalysis.app.ui.settings.adapters.ThemeAdapter

/**
 * 設置頁面 - 重新設計支援多種設置項目
 */
class SettingsActivity : AppCompatActivity(), ThemeManager.ThemeChangeListener {

    private lateinit var binding: ActivitySettingsBinding
    private lateinit var themeManager: ThemeManager
    private lateinit var themeAdapter: ThemeAdapter
    private var isThemeExpanded = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        themeManager = ThemeManager.getInstance(this)
        FontManager.initialize(this)

        setupToolbar()
        setupThemeSelection()
        setupClickListeners()
        updateCurrentThemeDisplay()
        updateCurrentFontDisplay()
        applyCurrentTheme()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "設置"
        }
    }
    
    private fun setupThemeSelection() {
        themeAdapter = ThemeAdapter(
            themes = themeManager.getAllThemes(),
            currentTheme = themeManager.getCurrentTheme(),
            onThemeSelected = { theme ->
                themeManager.setCurrentTheme(theme)
                updateCurrentThemeDisplay()
            }
        )

        binding.recyclerThemes.apply {
            layoutManager = LinearLayoutManager(this@SettingsActivity)
            adapter = themeAdapter
        }
    }

    private fun setupClickListeners() {
        // 主題顏色展開/收合
        binding.layoutThemeOption.setOnClickListener {
            toggleThemeExpansion()
        }

        // 字體選擇
        binding.layoutFontOption.setOnClickListener {
            showFontSelector()
        }

        // 字體測試 - 暫時移除，因為只有系統字體
        binding.layoutFontTest.setOnClickListener {
            // 可以在這裡添加簡單的字體預覽功能
            Toast.makeText(this, "目前只支援系統字體", Toast.LENGTH_SHORT).show()
        }
    }

    private fun updateCurrentThemeDisplay() {
        val currentTheme = themeManager.getCurrentTheme()
        binding.textCurrentTheme.text = currentTheme.name
    }

    private fun updateCurrentFontDisplay() {
        val currentFont = FontManager.getCurrentFont()
        binding.textCurrentFont.text = currentFont.displayName

        // 應用字體到顯示文字
        val typeface = FontManager.getTypeface(this, currentFont)
        if (typeface != null) {
            binding.textCurrentFont.typeface = typeface
        }
    }

    private fun showFontSelector() {
        val dialog = FontSelectorDialog(
            context = this,
            currentFont = FontManager.getCurrentFont()
        ) { selectedFont ->
            FontManager.setCurrentFont(this, selectedFont)
            updateCurrentFontDisplay()
            applyFontToApp()
        }
        dialog.show()
    }

    private fun applyFontToApp() {
        // 立即應用字體到當前Activity的所有TextView
        applyFontToAllViews()

        // 重新創建Activity以確保所有組件都使用新字體
        recreate()
    }

    private fun applyFontToAllViews() {
        val typeface = FontManager.getTypeface(this)
        if (typeface != null) {
            applyTypefaceToViewGroup(findViewById(android.R.id.content), typeface)
        }
    }

    private fun applyTypefaceToViewGroup(viewGroup: android.view.ViewGroup, typeface: android.graphics.Typeface) {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)
            when (child) {
                is android.widget.TextView -> {
                    child.typeface = typeface
                }
                is android.view.ViewGroup -> {
                    applyTypefaceToViewGroup(child, typeface)
                }
            }
        }
    }

    private fun toggleThemeExpansion() {
        isThemeExpanded = !isThemeExpanded

        val recyclerView = binding.recyclerThemes
        val expandIcon = binding.iconThemeExpand

        if (isThemeExpanded) {
            // 展開動畫
            expandIcon.setImageResource(R.drawable.ic_expand_less)
            expandRecyclerView(recyclerView)
        } else {
            // 收合動畫
            expandIcon.setImageResource(R.drawable.ic_expand_more)
            collapseRecyclerView(recyclerView)
        }
    }

    private fun expandRecyclerView(view: View) {
        view.visibility = View.VISIBLE

        // 測量RecyclerView的高度
        view.measure(
            View.MeasureSpec.makeMeasureSpec(view.width, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val targetHeight = view.measuredHeight

        // 動畫從0到目標高度
        val animator = ValueAnimator.ofInt(0, targetHeight)
        animator.addUpdateListener { animation ->
            val value = animation.animatedValue as Int
            val layoutParams = view.layoutParams
            layoutParams.height = value
            view.layoutParams = layoutParams
        }
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.start()
    }

    private fun collapseRecyclerView(view: View) {
        val initialHeight = view.height

        val animator = ValueAnimator.ofInt(initialHeight, 0)
        animator.addUpdateListener { animation ->
            val value = animation.animatedValue as Int
            val layoutParams = view.layoutParams
            layoutParams.height = value
            view.layoutParams = layoutParams

            if (value == 0) {
                view.visibility = View.GONE
            }
        }
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.start()
    }
    
    private fun applyCurrentTheme() {
        val currentTheme = themeManager.getCurrentTheme()

        // 應用主題到根佈局背景
        findViewById<android.view.View>(android.R.id.content).setBackgroundColor(currentTheme.getBackgroundColorInt())

        // 應用主題顏色到工具欄
        binding.toolbar.setBackgroundColor(currentTheme.getPrimaryColorInt())

        // 設置狀態欄顏色
        window.statusBarColor = currentTheme.getPrimaryDarkColorInt()
    }
    
    override fun onThemeChanged(theme: AppTheme) {
        // 主題變更時更新UI
        applyCurrentTheme()
        themeAdapter.updateCurrentTheme(theme)
        updateCurrentThemeDisplay()
    }
    
    override fun onResume() {
        super.onResume()
        themeManager.addThemeChangeListener(this)
    }
    
    override fun onPause() {
        super.onPause()
        themeManager.removeThemeChangeListener(this)
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
