// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageView iconFontExpand;

  @NonNull
  public final ImageView iconThemeExpand;

  @NonNull
  public final LinearLayout layoutFontOption;

  @NonNull
  public final LinearLayout layoutFontTest;

  @NonNull
  public final LinearLayout layoutThemeOption;

  @NonNull
  public final RecyclerView recyclerFonts;

  @NonNull
  public final RecyclerView recyclerThemes;

  @NonNull
  public final TextView textCurrentFont;

  @NonNull
  public final TextView textCurrentTheme;

  @NonNull
  public final Toolbar toolbar;

  private ActivitySettingsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageView iconFontExpand, @NonNull ImageView iconThemeExpand,
      @NonNull LinearLayout layoutFontOption, @NonNull LinearLayout layoutFontTest,
      @NonNull LinearLayout layoutThemeOption, @NonNull RecyclerView recyclerFonts,
      @NonNull RecyclerView recyclerThemes, @NonNull TextView textCurrentFont,
      @NonNull TextView textCurrentTheme, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.iconFontExpand = iconFontExpand;
    this.iconThemeExpand = iconThemeExpand;
    this.layoutFontOption = layoutFontOption;
    this.layoutFontTest = layoutFontTest;
    this.layoutThemeOption = layoutThemeOption;
    this.recyclerFonts = recyclerFonts;
    this.recyclerThemes = recyclerThemes;
    this.textCurrentFont = textCurrentFont;
    this.textCurrentTheme = textCurrentTheme;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon_font_expand;
      ImageView iconFontExpand = ViewBindings.findChildViewById(rootView, id);
      if (iconFontExpand == null) {
        break missingId;
      }

      id = R.id.icon_theme_expand;
      ImageView iconThemeExpand = ViewBindings.findChildViewById(rootView, id);
      if (iconThemeExpand == null) {
        break missingId;
      }

      id = R.id.layout_font_option;
      LinearLayout layoutFontOption = ViewBindings.findChildViewById(rootView, id);
      if (layoutFontOption == null) {
        break missingId;
      }

      id = R.id.layout_font_test;
      LinearLayout layoutFontTest = ViewBindings.findChildViewById(rootView, id);
      if (layoutFontTest == null) {
        break missingId;
      }

      id = R.id.layout_theme_option;
      LinearLayout layoutThemeOption = ViewBindings.findChildViewById(rootView, id);
      if (layoutThemeOption == null) {
        break missingId;
      }

      id = R.id.recycler_fonts;
      RecyclerView recyclerFonts = ViewBindings.findChildViewById(rootView, id);
      if (recyclerFonts == null) {
        break missingId;
      }

      id = R.id.recycler_themes;
      RecyclerView recyclerThemes = ViewBindings.findChildViewById(rootView, id);
      if (recyclerThemes == null) {
        break missingId;
      }

      id = R.id.text_current_font;
      TextView textCurrentFont = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentFont == null) {
        break missingId;
      }

      id = R.id.text_current_theme;
      TextView textCurrentTheme = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentTheme == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((CoordinatorLayout) rootView, iconFontExpand,
          iconThemeExpand, layoutFontOption, layoutFontTest, layoutThemeOption, recyclerFonts,
          recyclerThemes, textCurrentFont, textCurrentTheme, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
