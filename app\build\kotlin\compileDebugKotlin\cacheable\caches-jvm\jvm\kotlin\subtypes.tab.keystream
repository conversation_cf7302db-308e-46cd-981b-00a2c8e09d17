android.view.View+androidx.camera.core.ImageAnalysis.Analyzer?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener,com.erroranalysis.app.ui.base.ThemedActivityandroid.os.Parcelable(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback(androidx.appcompat.app.AppCompatActivitykotlin.Enum1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder(com.erroranalysis.app.utils.ImportResult1com.erroranalysis.app.utils.DocumentProcessResult androidx.viewbinding.ViewBindingandroid.app.Applicationandroidx.lifecycle.ViewModelandroid.app.Dialog7com.google.android.material.textfield.TextInputEditTextandroid.text.style.ImageSpan8androidx.recyclerview.widget.RecyclerView.ItemDecoration                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     