<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_settings_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="451" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/layout_theme_option" view="LinearLayout"><Expressions/><location startLine="70" startOffset="20" endLine="121" endOffset="34"/></Target><Target id="@+id/text_current_theme" view="TextView"><Expressions/><location startLine="103" startOffset="28" endLine="110" endOffset="64"/></Target><Target id="@+id/icon_theme_expand" view="ImageView"><Expressions/><location startLine="114" startOffset="24" endLine="119" endOffset="62"/></Target><Target id="@+id/recycler_themes" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="124" startOffset="20" endLine="130" endOffset="50"/></Target><Target id="@+id/layout_font_option" view="LinearLayout"><Expressions/><location startLine="133" startOffset="20" endLine="184" endOffset="34"/></Target><Target id="@+id/text_current_font" view="TextView"><Expressions/><location startLine="166" startOffset="28" endLine="173" endOffset="64"/></Target><Target id="@+id/icon_font_expand" view="ImageView"><Expressions/><location startLine="177" startOffset="24" endLine="182" endOffset="62"/></Target><Target id="@+id/recycler_fonts" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="187" startOffset="20" endLine="193" endOffset="50"/></Target><Target id="@+id/layout_font_test" view="LinearLayout"><Expressions/><location startLine="277" startOffset="20" endLine="320" endOffset="34"/></Target></Targets></Layout>