package com.erroranalysis.app.ui.theme

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Typeface
import androidx.core.content.res.ResourcesCompat
import com.erroranalysis.app.R

/**
 * 字體管理器
 * 管理應用中的自定義字體
 */
object FontManager {
    
    /**
     * 可用的字體類型
     */
    enum class FontType(val displayName: String, val fontResId: Int?) {
        SYSTEM("系統預設", null),
        CHENYU_LUOYAN("辰宇落雁體", R.font.chenyu_luoyan),
        NOTO_SANS_TC("思源黑體", R.font.noto_sans_tc)
        // 以下字體需要實際的字體文件才能使用
        // 添加字體文件後，取消註釋以下行：
        // QINGSONG_HANDWRITING("清松手寫體", R.font.qingsong_handwriting)
    }
    
    private var currentFontType = FontType.SYSTEM
    private val fontCache = mutableMapOf<FontType, Typeface?>()
    private const val PREFS_NAME = "font_settings"
    private const val KEY_CURRENT_FONT = "current_font"
    
    /**
     * 初始化字體管理器
     */
    fun initialize(context: Context) {
        loadFontSettings(context)
    }

    /**
     * 設置當前字體
     */
    fun setCurrentFont(context: Context, fontType: FontType) {
        android.util.Log.d("FontManager", "設置字體: ${fontType.displayName}")
        currentFontType = fontType
        saveFontSettings(context)
        android.util.Log.d("FontManager", "字體設置完成: ${fontType.displayName}")
    }

    /**
     * 設置當前字體（向後兼容）
     */
    fun setCurrentFont(fontType: FontType) {
        currentFontType = fontType
    }
    
    /**
     * 獲取當前字體
     */
    fun getCurrentFont(): FontType {
        return currentFontType
    }
    
    /**
     * 獲取字體Typeface
     */
    fun getTypeface(context: Context, fontType: FontType = currentFontType): Typeface? {
        // 如果是系統字體，返回null（使用預設）
        if (fontType == FontType.SYSTEM) {
            return null
        }
        
        // 檢查快取
        if (fontCache.containsKey(fontType)) {
            return fontCache[fontType]
        }
        
        // 載入字體 - 簡化版本
        val typeface = try {
            when (fontType) {
                FontType.SYSTEM -> {
                    android.util.Log.d("FontManager", "使用系統字體")
                    null
                }
                FontType.CHENYU_LUOYAN -> {
                    android.util.Log.d("FontManager", "開始載入辰宇落雁體")

                    // 使用ResourcesCompat載入字體
                    try {
                        val loadedTypeface = ResourcesCompat.getFont(context, R.font.chenyu_luoyan)
                        android.util.Log.d("FontManager", "字體載入成功")
                        loadedTypeface
                    } catch (e: Exception) {
                        android.util.Log.e("FontManager", "字體載入失敗", e)
                        null
                    }
                }
                FontType.NOTO_SANS_TC -> {
                    android.util.Log.d("FontManager", "開始載入思源黑體")

                    // 使用ResourcesCompat載入字體
                    try {
                        val loadedTypeface = ResourcesCompat.getFont(context, R.font.noto_sans_tc)
                        android.util.Log.d("FontManager", "思源黑體載入成功")
                        loadedTypeface
                    } catch (e: Exception) {
                        android.util.Log.e("FontManager", "思源黑體載入失敗", e)
                        null
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("FontManager", "載入字體異常: ${fontType.displayName}", e)
            null
        }
        
        // 快取字體
        fontCache[fontType] = typeface
        return typeface
    }
    
    /**
     * 獲取所有可用字體
     */
    fun getAvailableFonts(): List<FontType> {
        return FontType.values().toList()
    }

    /**
     * 獲取所有可用字體（檢查可用性）
     */
    fun getAvailableFonts(context: Context): List<FontType> {
        return FontType.values().filter { fontType ->
            val isAvailable = isFontAvailable(context, fontType)
            android.util.Log.d("FontManager", "字體 ${fontType.displayName} 可用性: $isAvailable")
            isAvailable
        }
    }
    
    /**
     * 清除字體快取
     */
    fun clearCache() {
        fontCache.clear()
    }
    
    /**
     * 檢查字體是否可用
     */
    fun isFontAvailable(context: Context, fontType: FontType): Boolean {
        if (fontType == FontType.SYSTEM) return true

        return try {
            fontType.fontResId?.let { resId ->
                android.util.Log.d("FontManager", "檢查字體可用性: ${fontType.displayName}, resId: $resId")

                // 嘗試載入字體
                val typeface = ResourcesCompat.getFont(context, resId)
                val isAvailable = typeface != null

                android.util.Log.d("FontManager", "字體 ${fontType.displayName} 可用性檢查結果: $isAvailable")

                if (typeface != null) {
                    android.util.Log.d("FontManager", "字體詳細信息: ${typeface.javaClass.simpleName}")
                }

                isAvailable
            } ?: false
        } catch (e: Exception) {
            android.util.Log.e("FontManager", "字體可用性檢查失敗: ${fontType.displayName}", e)
            false
        }
    }

    /**
     * 診斷字體問題
     */
    fun diagnoseFontIssues(context: Context): String {
        val diagnosis = StringBuilder()
        diagnosis.append("=== 字體診斷報告 ===\n")

        FontType.values().forEach { fontType ->
            diagnosis.append("\n字體: ${fontType.displayName}\n")

            if (fontType == FontType.SYSTEM) {
                diagnosis.append("  狀態: 系統字體 (總是可用)\n")
                return@forEach
            }

            try {
                val resId = fontType.fontResId
                diagnosis.append("  資源ID: $resId\n")

                if (resId != null) {
                    val typeface = ResourcesCompat.getFont(context, resId)
                    if (typeface != null) {
                        diagnosis.append("  載入狀態: 成功\n")
                        diagnosis.append("  字體類型: ${typeface.javaClass.simpleName}\n")
                        diagnosis.append("  是否粗體: ${typeface.isBold}\n")
                        diagnosis.append("  是否斜體: ${typeface.isItalic}\n")
                    } else {
                        diagnosis.append("  載入狀態: 失敗 (typeface為null)\n")
                    }
                } else {
                    diagnosis.append("  載入狀態: 失敗 (資源ID為null)\n")
                }
            } catch (e: Exception) {
                diagnosis.append("  載入狀態: 異常 - ${e.message}\n")
            }
        }

        return diagnosis.toString()
    }

    /**
     * 保存字體設置
     */
    private fun saveFontSettings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putString(KEY_CURRENT_FONT, currentFontType.name)
            .apply()
    }

    /**
     * 載入字體設置
     */
    private fun loadFontSettings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val fontName = prefs.getString(KEY_CURRENT_FONT, FontType.SYSTEM.name)

        currentFontType = try {
            FontType.valueOf(fontName ?: FontType.SYSTEM.name)
        } catch (e: Exception) {
            FontType.SYSTEM
        }
    }
}
