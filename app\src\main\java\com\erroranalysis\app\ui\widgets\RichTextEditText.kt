package com.erroranalysis.app.ui.widgets

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.GestureDetector
import android.widget.Toast
import com.google.android.material.textfield.TextInputEditText
import com.erroranalysis.app.utils.ImageStorageManager
import com.erroranalysis.app.utils.MathFormatHelper
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 * 支援圖文混合的EditText
 * 可以在文字中插入圖片，實現圖文混合編輯
 * 重新設計的圖片縮放系統，更簡潔可靠
 */
class RichTextEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextInputEditText(context, attrs, defStyleAttr) {

    companion object {
        private const val IMAGE_PLACEHOLDER = "\uFFFC"  // 使用標準圖片佔位符
        private const val MAX_IMAGE_WIDTH = 300 // dp
        private const val MAX_IMAGE_HEIGHT = 200 // dp
    }

    private val imageStorageManager by lazy { ImageStorageManager(context) }

    // 圖片縮放相關 - 簡化的實現
    private var scalingImageSpan: ZoomableImageSpan? = null
    private var initialDistance = 0f
    private var initialScale = 1.0f
    private var isImageScaling = false
    private val minScale = 0.3f
    private val maxScale = 4.0f

    // 縮放響應優化
    private var lastScaleTime = 0L
    private var lastAppliedScale = 1.0f

    // 圖片拖拽相關
    private var isDraggingImage = false
    private var dragImageSpan: ZoomableImageSpan? = null
    private var lastDragX = 0f
    private var lastDragY = 0f
    private var imageOffsetX = 0f
    private var imageOffsetY = 0f

    // 手勢檢測器 - 只保留必要的
    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            android.util.Log.d("RichTextEditText", "檢測到雙擊: (${e.x}, ${e.y})")

            // 強制處理圖片雙擊，無論當前是否有焦點
            val imageSpan = findImageSpanAtPosition(e.x, e.y)
            if (imageSpan != null) {
                android.util.Log.d("RichTextEditText", "雙擊圖片")

                // 如果在編輯模式，先清除焦點
                if (!isReadOnlyMode && hasFocus()) {
                    clearFocus()
                }

                return handleDoubleTap(e.x, e.y)
            }

            android.util.Log.d("RichTextEditText", "雙擊非圖片區域")
            return false
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // 單擊確認時的處理
            val imageSpan = findImageSpanAtPosition(e.x, e.y)
            if (imageSpan != null) {
                android.util.Log.d("RichTextEditText", "單擊圖片")
                // 如果在編輯模式，清除焦點
                if (!isReadOnlyMode && hasFocus()) {
                    clearFocus()
                    showToast("圖片區域 - 雙擊縮放")
                }
                return true
            }
            return false
        }
    })

    // 只讀模式標記
    private var isReadOnlyMode = false

    init {
        // 啟用滑動條並設置為持續顯示
        isVerticalScrollBarEnabled = true
        scrollBarStyle = SCROLLBARS_INSIDE_OVERLAY  // 改為OVERLAY，確保滾動條可見且可交互

        // 設置焦點行為 - 默認不自動獲得焦點
        isFocusable = true
        isFocusableInTouchMode = false  // 改為false，避免自動獲得焦點
        isClickable = true
        isLongClickable = true

        // 設置滑動條持續顯示並可交互
        isScrollbarFadingEnabled = false
        scrollBarDefaultDelayBeforeFade = 0
        scrollBarFadeDuration = 0

        // 確保滾動條可以交互
        setScrollBarSize(16) // 設置滾動條寬度

        // 默認不顯示光標
        isCursorVisible = false

        // 設置滾動行為 - 確保只在內容可滾動時滾動
        overScrollMode = OVER_SCROLL_NEVER  // 禁用過度滾動，避免影響父容器
        isScrollContainer = true  // 標記為滾動容器

        // 設置移動方法為滾動
        movementMethod = android.text.method.ScrollingMovementMethod.getInstance()

        android.util.Log.d("RichTextEditText", "初始化完成，默認無焦點，滾動條已啟用")
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 處理雙擊手勢 - 優先處理，確保圖片雙擊縮放能正常工作
        val gestureHandled = gestureDetector.onTouchEvent(event)
        if (gestureHandled) {
            return true
        }

        // 處理多指縮放 - 優先處理
        if (event.pointerCount >= 2) {
            return handleMultiTouchScaling(event)
        }

        // 處理單指拖拽
        if (event.pointerCount == 1) {
            val handled = handleImageDrag(event)
            if (handled) return true
        }

        // 如果正在縮放，結束縮放
        if (isImageScaling && event.actionMasked == MotionEvent.ACTION_UP) {
            endImageScaling()
        }

        // 如果正在拖拽，結束拖拽
        if (isDraggingImage && event.actionMasked == MotionEvent.ACTION_UP) {
            endImageDrag()
        }

        // 檢查是否點擊在滾動條上
        if (isPointOnScrollBar(event.x, event.y)) {
            android.util.Log.d("RichTextEditText", "點擊滾動條")
            // 如果在編輯模式，退出編輯模式
            if (!isReadOnlyMode && hasFocus()) {
                exitEditMode()
            }
            // 處理滾動條事件，阻止事件傳遞給父容器
            return handleScrollBarTouch(event)
        }

        // 智能觸控處理 - 區分圖片區域和文字區域
        return handleSmartTouch(event)
    }

    /**
     * 檢查點是否在滾動條上
     */
    private fun isPointOnScrollBar(x: Float, y: Float): Boolean {
        try {
            // 滾動條通常在右側
            val scrollBarWidth = 40f // 滾動條區域寬度
            val isOnRightEdge = x >= width - scrollBarWidth

            android.util.Log.d("RichTextEditText", "檢查滾動條: x=$x, width=$width, 在右側=$isOnRightEdge")
            return isOnRightEdge
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 處理滾動條觸控事件
     */
    private fun handleScrollBarTouch(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                android.util.Log.d("RichTextEditText", "滾動條按下")
                // 請求父容器不要攔截觸控事件
                parent?.requestDisallowInterceptTouchEvent(true)

                // 計算滾動位置
                val scrollRange = computeVerticalScrollRange() - computeVerticalScrollExtent()
                if (scrollRange > 0) {
                    val relativeY = (event.y - paddingTop) / (height - paddingTop - paddingBottom)
                    val targetScroll = (relativeY * scrollRange).toInt()
                    scrollTo(0, targetScroll)
                    android.util.Log.d("RichTextEditText", "滾動到位置: $targetScroll")
                }
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                android.util.Log.d("RichTextEditText", "滾動條拖拽")
                // 繼續處理拖拽
                val scrollRange = computeVerticalScrollRange() - computeVerticalScrollExtent()
                if (scrollRange > 0) {
                    val relativeY = (event.y - paddingTop) / (height - paddingTop - paddingBottom)
                    val targetScroll = (relativeY * scrollRange).toInt()
                    scrollTo(0, targetScroll)
                    android.util.Log.d("RichTextEditText", "拖拽滾動到位置: $targetScroll")
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                android.util.Log.d("RichTextEditText", "滾動條釋放")
                // 允許父容器重新攔截觸控事件
                parent?.requestDisallowInterceptTouchEvent(false)
                return true
            }
        }
        return false
    }

    /**
     * 智能觸控處理 - 精確控制文字編輯模式觸發
     */
    private fun handleSmartTouch(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                val imageSpan = findImageSpanAtPosition(event.x, event.y)

                if (imageSpan != null) {
                    // 點擊在圖片上 - 強制退出編輯模式
                    android.util.Log.d("RichTextEditText", "點擊圖片區域，強制退出編輯模式")

                    if (!isReadOnlyMode) {
                        exitEditMode()
                        showToast("圖片區域 - 雙擊縮放，雙指縮放")
                    }

                    // 完全阻止事件傳遞給EditText
                    return true

                } else {
                    // 點擊在非圖片區域
                    android.util.Log.d("RichTextEditText", "點擊非圖片區域")

                    if (!isReadOnlyMode) {
                        // 檢查是否在文字框內的空白區域
                        if (isPointInTextBlankArea(event.x, event.y)) {
                            // 在文字框內的空白區域，進入編輯模式
                            android.util.Log.d("RichTextEditText", "點擊文字框內空白區域，進入編輯模式")
                            enterEditMode()
                            return super.onTouchEvent(event)
                        } else {
                            // 不在文字框內空白區域，強制退出編輯模式
                            android.util.Log.d("RichTextEditText", "點擊文字框外，強制退出編輯模式")
                            exitEditMode()
                            // 允許滾動但不進入編輯模式
                            return super.onTouchEvent(event)
                        }
                    } else {
                        // 只讀模式：允許滾動
                        return super.onTouchEvent(event)
                    }
                }
            }

            MotionEvent.ACTION_MOVE -> {
                // 在編輯模式下，如果移動到文字框外，退出編輯模式
                if (!isReadOnlyMode && hasFocus()) {
                    if (!isPointInTextArea(event.x, event.y)) {
                        android.util.Log.d("RichTextEditText", "移動到文字框外，退出編輯模式")
                        exitEditMode()
                        return super.onTouchEvent(event)
                    }
                }

                return super.onTouchEvent(event)
            }

            else -> {
                // 其他事件：正常處理
                return super.onTouchEvent(event)
            }
        }
    }

    /**
     * 檢查點是否在文字區域內
     */
    private fun isPointInTextArea(x: Float, y: Float): Boolean {
        try {
            val layout = layout ?: return true // 如果沒有layout，假設在文字區域內

            // 獲取文字的實際邊界
            val textBounds = android.graphics.Rect()
            getLocalVisibleRect(textBounds)

            // 添加一些邊距，避免邊緣誤判
            val margin = 30f
            val inBounds = x >= paddingLeft + margin &&
                          x <= width - paddingRight - margin &&
                          y >= paddingTop + margin &&
                          y <= height - paddingBottom - margin

            android.util.Log.d("RichTextEditText", "檢查點($x, $y)是否在文字區域: $inBounds")
            return inBounds

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "檢查文字區域失敗", e)
            return true
        }
    }

    /**
     * 檢查點是否在文字框內的空白區域（不包括圖片和已有文字）
     */
    private fun isPointInTextBlankArea(x: Float, y: Float): Boolean {
        try {
            // 首先檢查是否在文字框內
            if (!isPointInTextArea(x, y)) {
                android.util.Log.d("RichTextEditText", "點不在文字框內")
                return false
            }

            // 檢查是否點擊在圖片上
            val imageSpan = findImageSpanAtPosition(x, y)
            if (imageSpan != null) {
                android.util.Log.d("RichTextEditText", "點在圖片上，不是空白區域")
                return false
            }

            // 檢查是否點擊在已有文字上
            val layout = layout ?: return true
            val text = text ?: return true

            val adjustedY = (y + scrollY).toInt()
            val line = layout.getLineForVertical(adjustedY)
            val offset = layout.getOffsetForHorizontal(line, x)

            // 檢查該位置是否有文字內容
            if (offset < text.length) {
                val char = text[offset]
                // 如果是空白字符或換行符，認為是空白區域
                val isBlankChar = char == ' ' || char == '\n' || char == '\t' || char == '\uFFFC'

                android.util.Log.d("RichTextEditText", "偏移 $offset 處的字符: '$char', 是空白: $isBlankChar")

                // 如果是空白字符，或者在文字末尾，認為是空白區域
                return isBlankChar || offset >= text.length - 1
            }

            // 如果偏移超出文字長度，認為是空白區域
            android.util.Log.d("RichTextEditText", "偏移超出文字長度，是空白區域")
            return true

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "檢查空白區域失敗", e)
            // 發生錯誤時，保守地允許編輯
            return true
        }
    }

    override fun computeVerticalScrollRange(): Int {
        // 確保滾動範圍正確計算
        return super.computeVerticalScrollRange()
    }

    override fun computeVerticalScrollOffset(): Int {
        // 確保滾動偏移正確計算
        return super.computeVerticalScrollOffset()
    }

    override fun computeVerticalScrollExtent(): Int {
        // 確保滾動範圍正確計算
        return super.computeVerticalScrollExtent()
    }

    override fun onScrollChanged(horiz: Int, vert: Int, oldHoriz: Int, oldVert: Int) {
        super.onScrollChanged(horiz, vert, oldHoriz, oldVert)
        // 確保滾動條正確更新
        awakenScrollBars()
    }

    override fun canScrollVertically(direction: Int): Boolean {
        // 確保可以垂直滾動
        return super.canScrollVertically(direction)
    }

    /**
     * 處理多指觸控縮放
     */
    private fun handleMultiTouchScaling(event: MotionEvent): Boolean {
        android.util.Log.d("RichTextEditText", "多指觸控: ${event.actionMasked}, 指針數: ${event.pointerCount}")

        when (event.actionMasked) {
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 第二根手指按下，開始縮放
                if (event.pointerCount == 2) {
                    val centerX = (event.getX(0) + event.getX(1)) / 2
                    val centerY = (event.getY(0) + event.getY(1)) / 2

                    android.util.Log.d("RichTextEditText", "雙指按下，中心點: ($centerX, $centerY)")

                    val imageSpan = findImageSpanAtPosition(centerX, centerY)
                    if (imageSpan != null) {
                        startImageScaling(imageSpan, event)
                        showToast("🎯 開始縮放圖片")
                        android.util.Log.d("RichTextEditText", "找到圖片，開始縮放")
                        return true
                    } else {
                        android.util.Log.d("RichTextEditText", "未找到圖片")
                        showToast("❌ 此位置沒有圖片")
                    }
                }
            }

            MotionEvent.ACTION_MOVE -> {
                // 處理縮放
                if (isImageScaling && scalingImageSpan != null) {
                    updateImageScale(event)
                    return true
                }
            }

            MotionEvent.ACTION_POINTER_UP -> {
                // 手指抬起，結束縮放
                if (event.pointerCount == 2) {
                    endImageScaling()
                }
            }
        }

        return isImageScaling
    }

    /**
     * 處理圖片拖拽 - 改進版本
     */
    private fun handleImageDrag(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 檢查是否點擊在放大的圖片上
                val imageSpan = findImageSpanAtPosition(event.x, event.y)
                if (imageSpan != null) {
                    val currentScale = getCurrentImageScale(imageSpan)
                    // 只有當圖片放大時才允許拖拽
                    if (currentScale > 1.2f) {
                        isDraggingImage = true
                        dragImageSpan = imageSpan
                        lastDragX = event.x
                        lastDragY = event.y

                        // 獲取當前偏移量
                        imageOffsetX = imageSpan.getOffsetX()
                        imageOffsetY = imageSpan.getOffsetY()

                        android.util.Log.d("RichTextEditText", "開始拖拽圖片，縮放: ${currentScale}x, 當前偏移: ($imageOffsetX, $imageOffsetY)")
                        showToast("拖拽模式")
                        return true
                    }
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingImage && dragImageSpan != null) {
                    val deltaX = event.x - lastDragX
                    val deltaY = event.y - lastDragY

                    // 更新偏移量
                    imageOffsetX += deltaX
                    imageOffsetY += deltaY

                    // 應用拖拽效果
                    applyImageDrag(dragImageSpan!!, imageOffsetX, imageOffsetY)

                    lastDragX = event.x
                    lastDragY = event.y

                    android.util.Log.d("RichTextEditText", "拖拽圖片: delta=($deltaX, $deltaY), 總偏移=($imageOffsetX, $imageOffsetY)")
                    return true
                }
            }

            MotionEvent.ACTION_UP -> {
                if (isDraggingImage) {
                    endImageDrag()
                    return true
                }
            }
        }

        return false
    }

    /**
     * 結束圖片拖拽
     */
    private fun endImageDrag() {
        isDraggingImage = false
        dragImageSpan = null
        imageOffsetX = 0f
        imageOffsetY = 0f
        android.util.Log.d("RichTextEditText", "結束拖拽圖片")
    }

    /**
     * 應用圖片拖拽效果 - 改進版本，創建自定義ImageSpan
     */
    private fun applyImageDrag(imageSpan: ZoomableImageSpan, offsetX: Float, offsetY: Float) {
        try {
            val originalWidth = imageSpan.drawable.intrinsicWidth
            val originalHeight = imageSpan.drawable.intrinsicHeight
            val currentScale = getCurrentImageScale(imageSpan)

            // 計算縮放後的尺寸
            val scaledWidth = (originalWidth * currentScale).toInt()
            val scaledHeight = (originalHeight * currentScale).toInt()

            // 限制拖拽範圍，避免圖片完全移出視野
            val maxOffsetX = scaledWidth * 0.4f
            val maxOffsetY = scaledHeight * 0.4f

            val clampedOffsetX = kotlin.math.max(-maxOffsetX, kotlin.math.min(maxOffsetX, offsetX))
            val clampedOffsetY = kotlin.math.max(-maxOffsetY, kotlin.math.min(maxOffsetY, offsetY))

            // 更新圖片的偏移量
            imageSpan.setOffset(clampedOffsetX, clampedOffsetY)

            // 設置新的邊界
            imageSpan.drawable.setBounds(
                clampedOffsetX.toInt(),
                clampedOffsetY.toInt(),
                (clampedOffsetX + scaledWidth).toInt(),
                (clampedOffsetY + scaledHeight).toInt()
            )

            // 強制重繪
            forceRedraw()

            android.util.Log.d("RichTextEditText", "拖拽應用: offset=(${clampedOffsetX}, ${clampedOffsetY}), 尺寸=${scaledWidth}x${scaledHeight}")

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "應用拖拽失敗", e)
        }
    }

    /**
     * 開始圖片縮放
     */
    private fun startImageScaling(imageSpan: ZoomableImageSpan, event: MotionEvent) {
        scalingImageSpan = imageSpan
        initialDistance = calculateDistance(event)
        initialScale = getCurrentImageScale(imageSpan)
        isImageScaling = true
    }

    /**
     * 更新圖片縮放 - 改進版本，提高縮小靈敏度
     */
    private fun updateImageScale(event: MotionEvent) {
        val currentDistance = calculateDistance(event)
        if (initialDistance > 0 && currentDistance > 0) {
            val scaleFactor = currentDistance / initialDistance
            val newScale = initialScale * scaleFactor
            val clampedScale = max(minScale, min(newScale, maxScale))

            // 獲取當前縮放比例
            val currentScale = getCurrentImageScale(scalingImageSpan!!)
            val scaleChange = kotlin.math.abs(clampedScale - currentScale)

            // 時間控制：避免過於頻繁的更新
            val currentTime = System.currentTimeMillis()
            val timeDelta = currentTime - lastScaleTime

            // 動態調整閾值：縮小時使用更小的閾值，提高靈敏度
            val threshold = if (clampedScale < currentScale) {
                0.01f // 縮小時更靈敏
            } else {
                0.03f // 放大時也提高靈敏度
            }

            // 檢查是否需要更新：變化足夠大 或 時間間隔足夠長
            val shouldUpdate = scaleChange > threshold ||
                              (timeDelta > 50 && kotlin.math.abs(clampedScale - lastAppliedScale) > 0.01f)

            if (shouldUpdate) {
                android.util.Log.d("RichTextEditText", "更新縮放: 距離比例=${scaleFactor}, 當前=${currentScale}, 新縮放=${clampedScale}, 閾值=${threshold}")

                applyImageScale(scalingImageSpan!!, clampedScale)
                showToast("縮放: ${String.format("%.1f", clampedScale)}x")

                lastScaleTime = currentTime
                lastAppliedScale = clampedScale
            }
        }
    }

    /**
     * 結束圖片縮放
     */
    private fun endImageScaling() {
        isImageScaling = false
        scalingImageSpan = null
        initialDistance = 0f
        initialScale = 1.0f

        // 如果圖片縮放到較小尺寸，重置拖拽偏移
        scalingImageSpan?.let { imageSpan ->
            val currentScale = getCurrentImageScale(imageSpan)
            if (currentScale <= 1.2f) {
                resetImagePosition(imageSpan)
            }
        }

        showToast("縮放結束")
    }

    /**
     * 重置圖片位置
     */
    private fun resetImagePosition(imageSpan: ZoomableImageSpan) {
        try {
            val originalWidth = imageSpan.drawable.intrinsicWidth
            val originalHeight = imageSpan.drawable.intrinsicHeight
            val currentScale = getCurrentImageScale(imageSpan)

            val scaledWidth = (originalWidth * currentScale).toInt()
            val scaledHeight = (originalHeight * currentScale).toInt()

            // 重置偏移量
            imageSpan.setOffset(0f, 0f)

            // 重置到原始位置（無偏移）
            imageSpan.drawable.setBounds(0, 0, scaledWidth, scaledHeight)

            // 強制重繪
            forceRedraw()

            android.util.Log.d("RichTextEditText", "重置圖片位置和偏移量")
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "重置圖片位置失敗", e)
        }
    }

    /**
     * 計算兩指距離
     */
    private fun calculateDistance(event: MotionEvent): Float {
        if (event.pointerCount < 2) return 0f
        val dx = event.getX(0) - event.getX(1)
        val dy = event.getY(0) - event.getY(1)
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 獲取圖片當前縮放比例
     */
    private fun getCurrentImageScale(imageSpan: ZoomableImageSpan): Float {
        val currentWidth = imageSpan.drawable.bounds.width()
        val originalWidth = imageSpan.drawable.intrinsicWidth
        return if (originalWidth > 0) currentWidth.toFloat() / originalWidth else 1.0f
    }

    /**
     * 應用圖片縮放
     */
    private fun applyImageScale(imageSpan: ZoomableImageSpan, scale: Float) {
        val originalWidth = imageSpan.drawable.intrinsicWidth
        val originalHeight = imageSpan.drawable.intrinsicHeight
        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()

        android.util.Log.d("RichTextEditText", "應用縮放: ${scale}x, 新尺寸: ${newWidth}x${newHeight}")

        imageSpan.drawable.setBounds(0, 0, newWidth, newHeight)

        // 強制重繪 - 多種方法確保視圖更新
        invalidate()
        requestLayout()

        // 立即強制重繪
        forceRedraw()
    }

    /**
     * 強制重繪視圖
     */
    private fun forceRedraw() {
        // 立即重繪
        invalidate()

        // 使用postDelayed避免過於頻繁的重繪
        removeCallbacks(redrawRunnable)
        postDelayed(redrawRunnable, 16) // 約60fps
    }

    // 重繪任務
    private val redrawRunnable = Runnable {
        try {
            // 保存當前狀態
            val currentText = text
            val currentSelection = selectionStart

            // 重新設置文字觸發完整重繪
            setText(currentText)

            // 恢復光標位置（僅在編輯模式下）
            if (!isReadOnlyMode) {
                try {
                    if (currentSelection >= 0 && currentSelection <= length()) {
                        setSelection(currentSelection)
                    }
                } catch (e: Exception) {
                    // 忽略光標設置錯誤
                }
            }

            android.util.Log.d("RichTextEditText", "延遲重繪完成")
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "重繪失敗", e)
        }
    }

    /**
     * 處理雙擊縮放
     */
    private fun handleDoubleTap(x: Float, y: Float): Boolean {
        val imageSpan = findImageSpanAtPosition(x, y)
        if (imageSpan != null) {
            val currentScale = getCurrentImageScale(imageSpan)
            val targetScale = if (currentScale > 1.5f) 1.0f else 2.0f

            android.util.Log.d("RichTextEditText", "雙擊縮放: 當前=${currentScale}, 目標=${targetScale}")

            // 如果縮放到較小尺寸，重置位置
            if (targetScale <= 1.2f) {
                resetImagePosition(imageSpan)
            }

            applyImageScale(imageSpan, targetScale)
            showToast("雙擊縮放: ${String.format("%.1f", targetScale)}x")
            return true
        }
        return false
    }

    /**
     * 查找指定位置的圖片 - 改進版本，支援多圖片精確檢測
     */
    private fun findImageSpanAtPosition(x: Float, y: Float): ZoomableImageSpan? {
        try {
            val text = text as? Spannable ?: return null
            val layout = layout ?: return null

            val imageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
            android.util.Log.d("RichTextEditText", "檢測圖片位置: ($x, $y), 總圖片數: ${imageSpans.size}")

            if (imageSpans.isEmpty()) {
                android.util.Log.d("RichTextEditText", "沒有圖片")
                return null
            }

            val adjustedY = (y + scrollY).toInt()

            // 改進的檢測方法：檢查每個圖片的實際視覺位置
            for ((index, imageSpan) in imageSpans.withIndex()) {
                val start = text.getSpanStart(imageSpan)
                val end = text.getSpanEnd(imageSpan)

                // 獲取圖片在文字中的行位置
                val startLine = layout.getLineForOffset(start)
                val endLine = layout.getLineForOffset(end - 1)

                // 獲取圖片的視覺邊界
                val lineTop = layout.getLineTop(startLine)
                val lineBottom = layout.getLineBottom(endLine)
                val lineLeft = layout.getLineLeft(startLine)
                val lineRight = layout.getLineRight(startLine)

                android.util.Log.d("RichTextEditText", "圖片 $index: 文字範圍 $start-$end, 行 $startLine-$endLine")
                android.util.Log.d("RichTextEditText", "圖片 $index: 視覺邊界 left=$lineLeft, top=$lineTop, right=$lineRight, bottom=$lineBottom")
                android.util.Log.d("RichTextEditText", "點擊位置: x=$x, adjustedY=$adjustedY")

                // 檢查點擊是否在圖片的視覺範圍內
                if (adjustedY >= lineTop && adjustedY <= lineBottom) {
                    // 在正確的行範圍內，進一步檢查水平位置
                    val offset = layout.getOffsetForHorizontal(startLine, x)

                    // 檢查偏移是否在圖片範圍內
                    if (offset >= start && offset < end) {
                        android.util.Log.d("RichTextEditText", "找到圖片 $index (精確匹配)")
                        return imageSpan
                    }

                    // 如果在同一行但偏移不匹配，檢查是否在圖片的水平範圍內
                    // 縮小檢測範圍，避免影響文字輸入
                    val imageWidth = imageSpan.drawable.bounds.width()
                    val imageLeft = layout.getPrimaryHorizontal(start)
                    val imageRight = imageLeft + imageWidth

                    // 添加邊距，只在圖片中心區域才認定為圖片點擊
                    val margin = 20f // 20像素邊距
                    if (x >= imageLeft + margin && x <= imageRight - margin) {
                        android.util.Log.d("RichTextEditText", "找到圖片 $index (視覺範圍匹配，有邊距)")
                        return imageSpan
                    }
                }
            }

            android.util.Log.d("RichTextEditText", "未找到圖片")

            // 備用方法：如果精確檢測失敗，使用簡單的偏移檢測
            return findImageSpanByOffset(x, y)

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "檢測圖片位置出錯", e)
            // 發生異常時使用備用方法
            return findImageSpanByOffset(x, y)
        }
    }

    /**
     * 備用圖片檢測方法 - 基於文字偏移
     */
    private fun findImageSpanByOffset(x: Float, y: Float): ZoomableImageSpan? {
        try {
            val text = text as? Spannable ?: return null
            val layout = layout ?: return null

            val imageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
            if (imageSpans.isEmpty()) return null

            val adjustedY = (y + scrollY).toInt()
            val line = layout.getLineForVertical(adjustedY)
            val offset = layout.getOffsetForHorizontal(line, x)

            android.util.Log.d("RichTextEditText", "備用檢測: 偏移=$offset")

            // 檢查每個圖片，找到最接近的
            var closestImageSpan: ZoomableImageSpan? = null
            var minDistance = Int.MAX_VALUE

            for ((index, imageSpan) in imageSpans.withIndex()) {
                val start = text.getSpanStart(imageSpan)
                val end = text.getSpanEnd(imageSpan)

                // 計算到圖片範圍的距離
                val distance = when {
                    offset >= start && offset < end -> 0 // 在範圍內
                    offset < start -> start - offset // 在圖片前
                    else -> offset - end + 1 // 在圖片後
                }

                android.util.Log.d("RichTextEditText", "備用檢測 圖片 $index: 範圍 $start-$end, 距離=$distance")

                if (distance < minDistance) {
                    minDistance = distance
                    closestImageSpan = imageSpan
                }
            }

            // 只有在精確匹配時才返回圖片（距離為0）
            if (minDistance == 0) {
                android.util.Log.d("RichTextEditText", "備用檢測找到圖片，精確匹配")
                return closestImageSpan
            }

            android.util.Log.d("RichTextEditText", "備用檢測未找到合適圖片")
            return null
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "備用檢測失敗", e)
            return null
        }
    }

    /**
     * 設置只讀模式
     */
    fun setReadOnlyMode(readOnly: Boolean) {
        isReadOnlyMode = readOnly
        android.util.Log.d("RichTextEditText", "設置只讀模式: $readOnly")

        if (readOnly) {
            // 只讀模式：禁用編輯但保持觸控響應
            isFocusable = true
            isFocusableInTouchMode = false
            isClickable = true
            isCursorVisible = false
            keyListener = null // 禁用鍵盤輸入

            // 清除焦點
            clearFocus()

            // 確保可以接收觸控事件進行縮放
            isLongClickable = true
            android.util.Log.d("RichTextEditText", "只讀模式設置完成")
        } else {
            // 編輯模式：設置可編輯但默認無焦點
            isFocusable = true
            isFocusableInTouchMode = false  // 不自動獲得焦點
            isClickable = true
            isCursorVisible = false  // 默認不顯示光標
            keyListener = android.text.method.TextKeyListener.getInstance()
            isLongClickable = true

            // 清除焦點，等待用戶主動點擊空白區域
            clearFocus()

            android.util.Log.d("RichTextEditText", "編輯模式設置完成，等待用戶點擊空白區域")
        }

        // 檢查並記錄當前的圖片數量
        checkImageSpans()
    }

    /**
     * 進入編輯模式
     */
    private fun enterEditMode() {
        if (!isReadOnlyMode) {
            isFocusableInTouchMode = true
            isCursorVisible = true
            requestFocus()

            android.util.Log.d("RichTextEditText", "進入編輯模式")
            showToast("編輯模式")
        }
    }

    /**
     * 退出編輯模式 - 強化版本
     */
    private fun exitEditMode() {
        if (!isReadOnlyMode) {
            // 強制退出編輯模式
            isFocusableInTouchMode = false
            isCursorVisible = false
            clearFocus()

            // 隱藏軟鍵盤
            val imm = context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
            imm.hideSoftInputFromWindow(windowToken, 0)

            android.util.Log.d("RichTextEditText", "強制退出編輯模式，隱藏鍵盤")
        }
    }

    /**
     * 檢查當前文字中的圖片數量
     */
    private fun checkImageSpans() {
        val text = text as? Spannable ?: return
        val imageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
        android.util.Log.d("RichTextEditText", "當前文字中有 ${imageSpans.size} 個圖片")
    }

    /**
     * 在手機上顯示提示訊息 - 已禁用
     */
    private fun showToast(message: String) {
        // Toast已禁用，保持方法以避免編譯錯誤
    }

    /**
     * 獲取文本中的所有圖片
     * @return 圖片列表，包含圖片和其在文本中的位置信息
     */
    fun getAllImages(): List<ImageInfo> {
        val images = mutableListOf<ImageInfo>()
        val spanned = text as? Spanned ?: return images

        val imageSpans = spanned.getSpans(0, spanned.length, ImageSpan::class.java)

        imageSpans.forEachIndexed { index, imageSpan ->
            val start = spanned.getSpanStart(imageSpan)
            val end = spanned.getSpanEnd(imageSpan)

            // 嘗試獲取圖片
            val bitmap = try {
                // 嘗試從drawable獲取
                val drawable = imageSpan.drawable
                if (drawable != null) {
                    drawableToBitmap(drawable)
                } else null
            } catch (e: Exception) {
                android.util.Log.w("RichTextEditText", "獲取圖片失敗", e)
                null
            }

            bitmap?.let {
                images.add(ImageInfo(
                    bitmap = it,
                    position = start,
                    index = index,
                    description = "圖片 ${index + 1}"
                ))
            }
        }

        return images
    }

    /**
     * 將Drawable轉換為Bitmap
     */
    private fun drawableToBitmap(drawable: Drawable): Bitmap? {
        return try {
            val bitmap = Bitmap.createBitmap(
                drawable.intrinsicWidth,
                drawable.intrinsicHeight,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            bitmap
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "轉換Drawable到Bitmap失敗", e)
            null
        }
    }

    /**
     * 圖片信息數據類
     */
    data class ImageInfo(
        val bitmap: Bitmap,
        val position: Int,
        val index: Int,
        val description: String
    )

    /**
     * 旋轉所有圖片90度
     */
    fun rotateAllImages90Degrees() {
        val spanned = text as? Spanned ?: return
        val imageSpans = spanned.getSpans(0, spanned.length, ImageSpan::class.java)

        if (imageSpans.isEmpty()) {
            android.util.Log.d("RichTextEditText", "沒有找到圖片可以旋轉")
            return
        }

        val newSpannable = SpannableStringBuilder(spanned)
        var rotatedCount = 0

        imageSpans.forEach { imageSpan ->
            try {
                val start = spanned.getSpanStart(imageSpan)
                val end = spanned.getSpanEnd(imageSpan)

                // 獲取原始圖片
                val originalBitmap = getImageFromSpan(imageSpan)
                originalBitmap?.let { bitmap ->
                    // 旋轉圖片-90度（逆時針）
                    val rotatedBitmap = rotateBitmap(bitmap, -90f)

                    // 保存旋轉後的圖片到存儲
                    val savedFileName = imageStorageManager.saveImage(rotatedBitmap)

                    if (savedFileName != null) {
                        // 縮放圖片以適應顯示
                        val scaledBitmap = scaleBitmap(rotatedBitmap)
                        val drawable = BitmapDrawable(context.resources, scaledBitmap)
                        drawable.setBounds(0, 0, scaledBitmap.width, scaledBitmap.height)

                        // 創建新的ZoomableImageSpan（與insertImage方法保持一致）
                        val newImageSpan = ZoomableImageSpan(drawable, savedFileName)

                        // 替換原有的ImageSpan
                        newSpannable.removeSpan(imageSpan)
                        newSpannable.setSpan(newImageSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

                        rotatedCount++
                        android.util.Log.d("RichTextEditText", "成功旋轉並保存圖片 $rotatedCount，文件名: $savedFileName")
                    } else {
                        android.util.Log.e("RichTextEditText", "保存旋轉後圖片失敗")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("RichTextEditText", "旋轉圖片失敗", e)
            }
        }

        if (rotatedCount > 0) {
            // 更新文本內容
            setText(newSpannable)
            android.util.Log.d("RichTextEditText", "完成旋轉 $rotatedCount 張圖片")
        }
    }

    /**
     * 從ImageSpan獲取圖片
     */
    private fun getImageFromSpan(imageSpan: ImageSpan): Bitmap? {
        return try {
            val drawable = imageSpan.drawable
            if (drawable != null) {
                drawableToBitmap(drawable)
            } else null
        } catch (e: Exception) {
            android.util.Log.w("RichTextEditText", "從ImageSpan獲取圖片失敗", e)
            null
        }
    }

    /**
     * 旋轉圖片
     */
    private fun rotateBitmap(bitmap: Bitmap, degrees: Float): Bitmap {
        val matrix = Matrix().apply {
            postRotate(degrees)
        }

        return Bitmap.createBitmap(
            bitmap, 0, 0,
            bitmap.width, bitmap.height,
            matrix, true
        )
    }

    /**
     * 創建ImageSpan
     */
    private fun createImageSpan(bitmap: Bitmap): ImageSpan {
        // 調整圖片大小以適應顯示
        val scaledBitmap = scaleImageToFit(bitmap)
        val drawable = BitmapDrawable(context.resources, scaledBitmap)
        drawable.setBounds(0, 0, scaledBitmap.width, scaledBitmap.height)

        return ImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
    }

    /**
     * 縮放圖片以適應顯示
     */
    private fun scaleImageToFit(bitmap: Bitmap): Bitmap {
        val maxWidth = dpToPx(MAX_IMAGE_WIDTH)
        val maxHeight = dpToPx(MAX_IMAGE_HEIGHT)

        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val scaleX = maxWidth.toFloat() / width
        val scaleY = maxHeight.toFloat() / height
        val scale = minOf(scaleX, scaleY)

        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * dp轉px
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }

    /**
     * 重寫onTextContextMenuItem以支援貼上圖片
     */
    override fun onTextContextMenuItem(id: Int): Boolean {
        when (id) {
            android.R.id.paste -> {
                // 檢查剪貼簿是否有圖片
                if (handleImagePaste()) {
                    return true
                }
                // 如果沒有圖片，執行正常的文字貼上
                return super.onTextContextMenuItem(id)
            }
            else -> return super.onTextContextMenuItem(id)
        }
    }

    /**
     * 處理圖片貼上
     */
    private fun handleImagePaste(): Boolean {
        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = clipboardManager.primaryClip

        if (clipData != null && clipData.itemCount > 0) {
            for (i in 0 until clipData.itemCount) {
                val item = clipData.getItemAt(i)

                // 檢查是否有圖片URI
                item.uri?.let { uri ->
                    if (isImageUri(uri)) {
                        try {
                            val bitmap = loadBitmapFromUri(uri)
                            if (bitmap != null) {
                                insertImage(bitmap)
                                android.util.Log.d("RichTextEditText", "成功貼上圖片: $uri")
                                return true
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("RichTextEditText", "貼上圖片失敗", e)
                        }
                    }
                }

                // 檢查是否有HTML內容中的圖片
                item.htmlText?.let { html ->
                    if (extractImageFromHtml(html)) {
                        return true
                    }
                }
            }
        }

        return false
    }

    /**
     * 檢查URI是否為圖片
     */
    private fun isImageUri(uri: Uri): Boolean {
        val mimeType = context.contentResolver.getType(uri)
        return mimeType?.startsWith("image/") == true
    }

    /**
     * 從URI載入圖片
     */
    private fun loadBitmapFromUri(uri: Uri): Bitmap? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val bitmap = android.graphics.BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            bitmap
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "從URI載入圖片失敗: $uri", e)
            null
        }
    }

    /**
     * 從HTML內容中提取圖片（處理從網頁複製的圖片）
     */
    private fun extractImageFromHtml(html: String): Boolean {
        // 這裡可以解析HTML中的<img>標籤
        // 目前先返回false，因為這個功能比較複雜
        return false
    }

    /**
     * 重寫onKeyDown以支援Ctrl+V快捷鍵
     */
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (event != null && event.isCtrlPressed && keyCode == KeyEvent.KEYCODE_V) {
            // Ctrl+V 貼上
            if (handleImagePaste()) {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }







    /**
     * 在當前光標位置插入圖片
     */
    fun insertImage(bitmap: Bitmap, imagePath: String? = null) {
        // 保存圖片到本地存儲
        val savedFileName = imageStorageManager.saveImage(bitmap)
        if (savedFileName == null) {
            // 圖片保存失敗
            return
        }

        val scaledBitmap = scaleBitmap(bitmap)
        val drawable = BitmapDrawable(resources, scaledBitmap)

        // 設置drawable的邊界
        val width = scaledBitmap.width
        val height = scaledBitmap.height
        drawable.setBounds(0, 0, width, height)

        // 建立ImageSpan，使用保存的文件名
        val imageSpan = ZoomableImageSpan(drawable, savedFileName)

        // 獲取當前文字和光標位置
        val currentText = text ?: SpannableStringBuilder()
        val cursorPosition = selectionStart.coerceAtLeast(0)

        // 建立新的SpannableStringBuilder
        val newText = SpannableStringBuilder(currentText)

        // 在光標位置插入圖片佔位符
        newText.insert(cursorPosition, IMAGE_PLACEHOLDER)

        // 設置ImageSpan
        newText.setSpan(
            imageSpan,
            cursorPosition,
            cursorPosition + IMAGE_PLACEHOLDER.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 更新文字
        setText(newText)

        // 將光標移動到圖片後面
        setSelection(cursorPosition + IMAGE_PLACEHOLDER.length)
    }

    /**
     * 縮放圖片到合適大小
     */
    private fun scaleBitmap(bitmap: Bitmap): Bitmap {
        val density = resources.displayMetrics.density
        val maxWidthPx = (MAX_IMAGE_WIDTH * density).toInt()
        val maxHeightPx = (MAX_IMAGE_HEIGHT * density).toInt()
        
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height
        
        // 計算縮放比例
        val scaleX = maxWidthPx.toFloat() / originalWidth
        val scaleY = maxHeightPx.toFloat() / originalHeight
        val scale = minOf(scaleX, scaleY, 1.0f) // 不放大，只縮小
        
        if (scale < 1.0f) {
            val newWidth = (originalWidth * scale).toInt()
            val newHeight = (originalHeight * scale).toInt()
            return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
        }
        
        return bitmap
    }

    /**
     * 獲取純文字內容（移除圖片佔位符）
     */
    fun getPlainText(): String {
        val text = text?.toString() ?: ""
        return text.replace(IMAGE_PLACEHOLDER, "")
    }

    /**
     * 檢查是否有內容（文字或圖片）
     */
    fun hasContent(): Boolean {
        val text = text?.toString() ?: ""
        val plainText = text.replace(IMAGE_PLACEHOLDER, "").trim()
        val hasImages = text.contains(IMAGE_PLACEHOLDER)
        return plainText.isNotEmpty() || hasImages
    }

    /**
     * 獲取圖文混合內容的JSON表示
     */
    fun getRichContent(): String {
        val text = text as? Spannable ?: return getPlainText()
        val content = mutableListOf<ContentItem>()
        
        var lastIndex = 0
        val imageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
        
        for (imageSpan in imageSpans) {
            val start = text.getSpanStart(imageSpan)
            val end = text.getSpanEnd(imageSpan)
            
            // 添加圖片前的文字
            if (start > lastIndex) {
                val textContent = text.substring(lastIndex, start)
                if (textContent.isNotEmpty()) {
                    content.add(ContentItem("text", textContent))
                }
            }
            
            // 添加圖片
            content.add(ContentItem("image", imageSpan.imageFileName ?: ""))
            
            lastIndex = end
        }
        
        // 添加最後的文字
        if (lastIndex < text.length) {
            val textContent = text.substring(lastIndex)
            if (textContent.isNotEmpty()) {
                content.add(ContentItem("text", textContent))
            }
        }
        
        return com.google.gson.Gson().toJson(content)
    }

    /**
     * 安全地載入圖片，避免記憶體溢出
     */
    private fun loadImageSafely(fileName: String): Bitmap? {
        return try {
            val bitmap = imageStorageManager.loadImage(fileName)
            if (bitmap != null) {
                // 確保圖片尺寸合理
                scaleBitmap(bitmap)
            } else {
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "安全載入圖片失敗: $fileName", e)
            null
        }
    }

    /**
     * 從JSON恢復圖文混合內容（簡化安全版本）
     */
    fun setRichContent(jsonContent: String) {
        android.util.Log.d("RichTextEditText", "開始載入內容，長度: ${jsonContent.length}")

        // 先嘗試設置純文字，確保基本功能正常
        try {
            // 檢查是否為JSON格式
            if (!jsonContent.trim().startsWith("[")) {
                setText(jsonContent)
                android.util.Log.d("RichTextEditText", "純文字內容載入完成")
                return
            }

            // 嘗試解析JSON並載入圖文內容
            loadRichContentSafely(jsonContent)

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "載入圖文內容失敗，降級為純文字", e)
            try {
                setText(jsonContent)
            } catch (e2: Exception) {
                android.util.Log.e("RichTextEditText", "設置純文字也失敗", e2)
                setText("內容載入失敗")
            }
        }
    }

    /**
     * 設置帶有 Markdown 格式的文字
     * 支援 **粗體** 和 `代碼` 格式
     */
    fun setMarkdownText(markdownText: String) {
        try {
            val spannableString = processMarkdownFormatting(markdownText)
            setText(spannableString)
            android.util.Log.d("RichTextEditText", "Markdown文字設置完成")
        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "Markdown處理失敗，使用純文字", e)
            setText(markdownText)
        }
    }

    /**
     * 處理 Markdown 格式
     */
    private fun processMarkdownFormatting(text: String): SpannableStringBuilder {
        val spannableBuilder = SpannableStringBuilder()
        var currentIndex = 0

        // 處理粗體標記 **text**
        val boldPattern = Regex("\\*\\*([^*]+?)\\*\\*")
        val boldMatches = boldPattern.findAll(text).toList()

        // 處理代碼標記 `text`
        val codePattern = Regex("`([^`]+?)`")
        val codeMatches = codePattern.findAll(text).toList()

        // 合併所有匹配並按位置排序
        val allMatches = (boldMatches.map { MatchInfo(it, "bold") } +
                         codeMatches.map { MatchInfo(it, "code") })
            .sortedBy { it.match.range.first }

        for (matchInfo in allMatches) {
            val match = matchInfo.match
            val type = matchInfo.type

            // 添加匹配前的普通文字
            if (currentIndex < match.range.first) {
                spannableBuilder.append(text.substring(currentIndex, match.range.first))
            }

            // 添加格式化的文字（移除標記）
            val content = match.groupValues[1]
            val start = spannableBuilder.length
            spannableBuilder.append(content)
            val end = spannableBuilder.length

            // 應用格式
            when (type) {
                "bold" -> {
                    spannableBuilder.setSpan(
                        android.text.style.StyleSpan(android.graphics.Typeface.BOLD),
                        start,
                        end,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                "code" -> {
                    spannableBuilder.setSpan(
                        android.text.style.TypefaceSpan("monospace"),
                        start,
                        end,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    spannableBuilder.setSpan(
                        android.text.style.BackgroundColorSpan(0xFFE8E8E8.toInt()),
                        start,
                        end,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }

            currentIndex = match.range.last + 1
        }

        // 添加剩餘的普通文字
        if (currentIndex < text.length) {
            spannableBuilder.append(text.substring(currentIndex))
        }

        return spannableBuilder
    }

    /**
     * 匹配信息數據類
     */
    private data class MatchInfo(val match: MatchResult, val type: String)

    /**
     * 安全載入圖文混合內容
     */
    private fun loadRichContentSafely(jsonContent: String) {
        try {
            val content = com.google.gson.Gson().fromJson(
                jsonContent,
                Array<ContentItem>::class.java
            ).toList()

            val spannableBuilder = SpannableStringBuilder()

            for (item in content) {
                when (item.type) {
                    "text" -> {
                        spannableBuilder.append(item.content)
                    }
                    "image" -> {
                        // 載入圖片
                        val bitmap = loadImageSafely(item.content)
                        if (bitmap != null) {
                            val drawable = BitmapDrawable(resources, bitmap)
                            drawable.setBounds(0, 0, bitmap.width, bitmap.height)

                            val imageSpan = ZoomableImageSpan(drawable, item.content)
                            val start = spannableBuilder.length
                            spannableBuilder.append(IMAGE_PLACEHOLDER)
                            spannableBuilder.setSpan(
                                imageSpan,
                                start,
                                start + IMAGE_PLACEHOLDER.length,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        } else {
                            spannableBuilder.append("[圖片]")
                        }
                    }
                }
            }

            setText(spannableBuilder)
            android.util.Log.d("RichTextEditText", "圖文內容載入完成")

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "解析JSON失敗", e)
            throw e // 重新拋出異常，讓上層處理
        }
    }

    /**
     * 可縮放的ImageSpan，保存圖片文件名和偏移量
     */
    private class ZoomableImageSpan(
        drawable: Drawable,
        val imageFileName: String?
    ) : ImageSpan(drawable, ALIGN_BASELINE) {

        private var offsetX = 0f
        private var offsetY = 0f

        fun setOffset(x: Float, y: Float) {
            offsetX = x
            offsetY = y
        }

        fun getOffsetX(): Float = offsetX
        fun getOffsetY(): Float = offsetY

        override fun draw(
            canvas: android.graphics.Canvas,
            text: CharSequence?,
            start: Int,
            end: Int,
            x: Float,
            top: Int,
            y: Int,
            bottom: Int,
            paint: android.graphics.Paint
        ) {
            // 保存畫布狀態
            canvas.save()

            // 應用偏移量
            canvas.translate(offsetX, offsetY)

            // 調用父類的繪製方法
            super.draw(canvas, text, start, end, x, top, y, bottom, paint)

            // 恢復畫布狀態
            canvas.restore()
        }
    }

    /**
     * 專門用於只讀顯示的安全方法
     */
    fun setRichContentForDisplay(jsonContent: String) {
        try {
            android.util.Log.d("RichTextEditText", "只讀模式載入內容")

            // 檢查是否為JSON格式
            if (!jsonContent.trim().startsWith("[")) {
                setText(jsonContent)
                return
            }

            // 嘗試解析JSON
            val content = com.google.gson.Gson().fromJson(
                jsonContent,
                Array<ContentItem>::class.java
            ).toList()

            val spannableBuilder = SpannableStringBuilder()

            for (item in content) {
                when (item.type) {
                    "text" -> {
                        spannableBuilder.append(item.content)
                    }
                    "image" -> {
                        // 只讀模式：嘗試載入圖片，失敗則顯示佔位符
                        val bitmap = loadImageSafely(item.content)
                        if (bitmap != null) {
                            val drawable = BitmapDrawable(resources, bitmap)
                            drawable.setBounds(0, 0, bitmap.width, bitmap.height)

                            val imageSpan = ZoomableImageSpan(drawable, item.content)
                            val start = spannableBuilder.length
                            spannableBuilder.append(IMAGE_PLACEHOLDER)
                            spannableBuilder.setSpan(
                                imageSpan,
                                start,
                                start + IMAGE_PLACEHOLDER.length,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        } else {
                            spannableBuilder.append("[圖片]")
                        }
                    }
                }
            }

            setText(spannableBuilder)

        } catch (e: Exception) {
            android.util.Log.e("RichTextEditText", "只讀模式載入失敗", e)
            setText(jsonContent)
        }
    }

    /**
     * 內容項目資料類別
     */
    private data class ContentItem(
        val type: String,
        val content: String
    )
}
