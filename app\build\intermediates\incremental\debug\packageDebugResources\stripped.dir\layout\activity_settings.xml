<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 外觀設置區域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 外觀設置標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="🎨 外觀"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="個性化您的應用外觀"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- 主題顏色選項 -->
                    <LinearLayout
                        android:id="@+id/layout_theme_option"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:paddingTop="0dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_palette"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="主題顏色"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/text_current_theme"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="經典藍"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/icon_theme_expand"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_expand_more"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- 主題選擇器 (可展開/收合) - 緊跟在主題顏色選項後面 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_themes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:padding="16dp"
                        android:paddingTop="0dp" />

                    <!-- 字體選擇選項 -->
                    <LinearLayout
                        android:id="@+id/layout_font_option"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:paddingTop="0dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_font"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="字體選擇"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/text_current_font"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="系統預設"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/icon_font_expand"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_expand_more"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- 字體選擇器 (可展開/收合) - 緊跟在字體選擇選項後面 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_fonts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:padding="16dp"
                        android:paddingTop="0dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 功能設置區域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="⚙️ 功能設置"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- 相機設置選項 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_camera"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="相機設置"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="拍照品質、自動對焦等"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="即將推出"
                            android:textSize="12sp"
                            android:textColor="@color/text_hint"
                            android:background="@drawable/bg_tag_rounded"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- 字體測試選項 -->
                    <LinearLayout
                        android:id="@+id/layout_font_test"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_font"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="字體測試"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="測試字體載入功能"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 學習設置選項 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_book"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="學習設置"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="複習提醒、學習目標等"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="即將推出"
                            android:textSize="12sp"
                            android:textColor="@color/text_hint"
                            android:background="@drawable/bg_tag_rounded"
                            android:padding="6dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 關於設置區域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="ℹ️ 關於應用"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- 版本信息 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_info"
                            android:layout_marginEnd="16dp"
                            app:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="版本信息"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="智學分析 v1.0"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
